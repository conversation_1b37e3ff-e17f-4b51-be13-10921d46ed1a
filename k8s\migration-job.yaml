apiVersion: batch/v1
kind: Job
metadata:
  name: ai-nest-backend-migration
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: migration
spec:
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: ai-nest-backend
        component: migration
    spec:
      restartPolicy: OnFailure
      initContainers:
      - name: wait-for-postgres
        image: postgres:13
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
      containers:
      - name: migration
        image: docker.io/saipriya104/ai-nest-backend:latest
        command: ['sh', '-c']
        args:
        - |
          echo "Running database migrations..."
          npm run migration:latest
          echo "Running database seeds..."
          npm run db:seed
          echo "Database initialization completed!"
        env:
        # Database configuration
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_NAME
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: NODE_ENV

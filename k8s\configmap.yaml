apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-nest-backend-config
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: config
data:
  NODE_ENV: "production"
  PORT: "8080"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "userauth"
  JWT_EXPIRATION: "86400000"
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: '"No Reply" <<EMAIL>>'
  APP_URL: "http://localhost:3000"
  API_URL: "http://localhost:3000"
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"
  OAUTH2_AUTHORIZED_REDIRECT_URIS: "http://localhost:8080/oauth2/callback/google,http://localhost:3000/oauth2/redirect,myandroidapp://oauth2/redirect,myiosapp://oauth2/redirect"
  GOOGLE_REDIRECT_URI: "http://localhost:8080/oauth2/callback/google"
  GOOGLE_SCOPE: "email,profile,openid"

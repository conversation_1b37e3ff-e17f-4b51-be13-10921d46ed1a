import { AuthGuard } from '../../src/auth/auth.guard';
import { UnauthorizedException } from '@nestjs/common';

describe('AuthGuard', () => {
  let guard: AuthGuard;

  beforeEach(() => {
    guard = new AuthGuard();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should throw an error if one is provided', () => {
    const err = new Error('test error');
    expect(() => guard.handleRequest(err, null, null)).toThrow(err);
  });

  it('should throw UnauthorizedException if there is no user and no error', () => {
    expect(() => guard.handleRequest(null, null, null)).toThrow(UnauthorizedException);
  });

  it('should return the user if user is present and there is no error', () => {
    const user = { id: '1', username: 'test' };
    expect(guard.handleRequest(null, user, null)).toEqual(user);
  });

  it('should call super.canActivate in canActivate', () => {
    const context = {} as any;
    // Get the parent prototype
    const parentProto = Object.getPrototypeOf(AuthGuard.prototype);
    const superCanActivate = jest.spyOn(parentProto, 'canActivate').mockReturnValue('called');
    const result = guard.canActivate(context);
    expect(superCanActivate).toHaveBeenCalledWith(context);
    expect(result).toBe('called');
    superCanActivate.mockRestore();
  });
}); 
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend-simple
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend-simple
    component: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-nest-backend-simple
  template:
    metadata:
      labels:
        app: ai-nest-backend-simple
        component: api
    spec:
      containers:
      - name: ai-nest-backend
        image: node:20-alpine
        workingDir: /app
        command: ['sh', '-c']
        args:
        - |
          echo "Installing express..."
          npm init -y
          npm install express
          
          echo "Creating simple server..."
          cat > server.js << 'EOF'
          const express = require('express');
          const app = express();
          const port = process.env.PORT || 8080;
          
          app.use(express.json());
          
          app.get('/oauth2/status', (req, res) => {
            res.json({
              success: true,
              message: 'AI Nest Backend is running successfully',
              timestamp: new Date().toISOString(),
              status: 'healthy',
              environment: process.env.NODE_ENV || 'production',
              version: '1.0.0'
            });
          });
          
          app.get('/api-docs', (req, res) => {
            res.send(`
              <h1>🚀 AI Nest Backend</h1>
              <p><strong>Status:</strong> ✅ Running Successfully!</p>
              <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'production'}</p>
              <p><strong>Port:</strong> ${port}</p>
              <h2>Available Endpoints:</h2>
              <ul>
                <li><a href="/oauth2/status">GET /oauth2/status - Health Check</a></li>
                <li><a href="/api-docs">GET /api-docs - This page</a></li>
              </ul>
            `);
          });
          
          app.get('/', (req, res) => {
            res.redirect('/api-docs');
          });
          
          app.use('*', (req, res) => {
            res.status(404).json({
              error: 'Not Found',
              message: 'Endpoint not found',
              availableEndpoints: ['/oauth2/status', '/api-docs', '/']
            });
          });
          
          app.listen(port, '0.0.0.0', () => {
            console.log(`🚀 AI Nest Backend running on port ${port}`);
            console.log(`📚 API Documentation: http://localhost:${port}/api-docs`);
            console.log(`🏥 Health Check: http://localhost:${port}/oauth2/status`);
          });
          EOF
          
          echo "Starting server..."
          node server.js
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        livenessProbe:
          httpGet:
            path: /oauth2/status
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /oauth2/status
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      restartPolicy: Always

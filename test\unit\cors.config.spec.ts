import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { corsConfig } from '../../src/config/cors.config';
import { ConfigService } from '@nestjs/config';

describe('CORS Configuration', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [corsConfig],
        }),
      ],
    }).compile();
  });

  it('should load default CORS configuration', async () => {
    const configService = module.get(ConfigService);
    const cors = configService.get('cors');

    expect(cors).toBeDefined();
    expect(cors.allowedOrigins).toEqual([
      'http://localhost:3000',
      'http://localhost:3001',
    ]);
    expect(cors.allowedMethods).toEqual([
      'GET',
      'POST',
      'PUT',
      'DELETE',
      'PATCH',
      'OPTIONS'
    ]);
    expect(cors.allowCredentials).toBe(true);
    expect(cors.maxAge).toBe(3600);
  });

  it('should load CORS configuration from environment variables', async () => {
    const originalEnv = process.env;
    
    process.env = {
      ...originalEnv,
      CORS_ALLOWED_ORIGINS: 'http://example.com,http://test.com',
      CORS_ALLOWED_METHODS: 'GET,POST',
      CORS_ALLOW_CREDENTIALS: 'false',
      CORS_MAX_AGE: '7200',
    };

    const testModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [corsConfig],
        }),
      ],
    }).compile();

    const configService = testModule.get(ConfigService);
    const cors = configService.get('cors');

    expect(cors.allowedOrigins).toEqual(['http://example.com', 'http://test.com']);
    expect(cors.allowedMethods).toEqual(['GET', 'POST']);
    expect(cors.allowCredentials).toBe(false);
    expect(cors.maxAge).toBe(7200);

    process.env = originalEnv;
  });
}); 
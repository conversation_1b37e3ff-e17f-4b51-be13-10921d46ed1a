import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('user_verification', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('contact_value', 255).notNullable().unique();
    table.string('verification_token', 255).notNullable().unique();
    table.string('name', 100).notNullable();
    table.string('mobile_number', 10).notNullable();
    table.timestamp('expires_at').notNullable();
    table.boolean('verified').notNullable().defaultTo(false);
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('verified_at').nullable();
    table.string('ip_address', 15).nullable();
    table.text('device_details').nullable();
  });
  await knex.raw(`CREATE UNIQUE INDEX idx_verification_token_unverified ON user_verification(verification_token) WHERE verified = false`);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('user_verification');
} 
import { PostgreSqlContainer } from '@testcontainers/postgresql';
import { StartedTestContainer } from 'testcontainers';
import knex, { Knex } from 'knex';
import path from 'path';

export interface TestDbContext {
  container: StartedTestContainer;
  knex: Knex;
}

export async function setupTestDb(): Promise<TestDbContext> {
  console.log('🔧 Setting up test database...');

  // Configure container with CI-friendly settings
  const containerBuilder = new PostgreSqlContainer('postgres:15')
    .withDatabase('test_db')
    .withUsername('test_user')
    .withPassword('test_password');

  // Add CI-specific configurations
  if (process.env.CI_ENVIRONMENT === 'true') {
    console.log('🔧 CI environment detected, applying CI-specific configurations...');
    containerBuilder.withStartupTimeout(180000); // 3 minutes startup timeout for CI
  }

  const container = await containerBuilder.start();
  console.log('✅ Test database container started successfully');

  const db = knex({
    client: 'pg',
    connection: {
      host: container.getHost(),
      port: container.getPort(),
      user: container.getUsername(),
      password: container.getPassword(),
      database: container.getDatabase(),
      // Add connection timeout settings
      connectionTimeout: 60000, // 60 seconds
    },
    pool: {
      min: 1,
      max: 5,
      acquireTimeoutMillis: 60000, // 60 seconds
      createTimeoutMillis: 30000, // 30 seconds
      destroyTimeoutMillis: 5000, // 5 seconds
      idleTimeoutMillis: 30000, // 30 seconds
      createRetryIntervalMillis: 200,
    },
    migrations: {
      directory: path.join(__dirname, '../../src/database/migrations'),
    },
  });

  // Wait for DB to be ready with extended timeout for CI
  const maxRetries = process.env.CI_ENVIRONMENT === 'true' ? 30 : 10;
  const retryDelay = process.env.CI_ENVIRONMENT === 'true' ? 2000 : 1000;

  console.log(`🔧 Waiting for database to be ready (max ${maxRetries} retries)...`);
  let ready = false;
  for (let i = 0; i < maxRetries; i++) {
    try {
      await db.raw('SELECT 1');
      ready = true;
      console.log('✅ Database is ready!');
      break;
    } catch (e) {
      console.log(`⏳ Database not ready yet (attempt ${i + 1}/${maxRetries}), retrying in ${retryDelay}ms...`);
      await new Promise((res) => setTimeout(res, retryDelay));
    }
  }
  if (!ready) {
    console.error('❌ Test DB did not become ready after maximum retries');
    throw new Error(`Test DB did not become ready after ${maxRetries} attempts`);
  }

  // Run migrations with error handling
  try {
    console.log('🔧 Running database migrations...');
    await db.migrate.latest();
    console.log('✅ Database migrations completed successfully');
  } catch (error) {
    console.error('❌ Failed to run database migrations:', error);
    throw error;
  }

  // Optionally run seeds if you have them
  try {
    console.log('🔧 Running database seeds...');
    await seedDatabase(db);
    console.log('✅ Database seeds completed successfully');
  } catch (error) {
    console.error('❌ Failed to run database seeds:', error);
    throw error;
  }

  console.log('✅ Test database setup completed successfully');
  return { container, knex: db };
}

export async function teardownTestDb(ctx: TestDbContext | undefined) {
  if (!ctx) return;

  console.log('🔧 Tearing down test database...');

  try {
    if (ctx.knex) {
      console.log('🔧 Closing database connections...');
      await ctx.knex.destroy();
      console.log('✅ Database connections closed');
    }
  } catch (error) {
    console.error('❌ Error closing database connections:', error);
  }

  try {
    if (ctx.container) {
      console.log('🔧 Stopping test container...');
      await ctx.container.stop();
      console.log('✅ Test container stopped');
    }
  } catch (error) {
    console.error('❌ Error stopping test container:', error);
  }

  console.log('✅ Test database teardown completed');
}

export async function cleanDb(knex: Knex) {
  // Truncate all tables (Postgres only)
  const tables = await knex.raw(`
    SELECT tablename FROM pg_tables WHERE schemaname = 'public';
  `);
  for (const row of tables.rows) {
    await knex.raw(`TRUNCATE TABLE "${row.tablename}" RESTART IDENTITY CASCADE;`);
  }
}

export async function seedDatabase(knex: Knex) {
  // Run all seeds in the seeds/ folder before tests
  await knex.seed.run({ directory: path.resolve(__dirname, '../../seeds') });
} 
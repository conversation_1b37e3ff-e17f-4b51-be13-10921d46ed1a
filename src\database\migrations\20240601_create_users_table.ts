import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email', 255).notNullable().unique();
    table.string('password', 255).notNullable();
    table.string('name', 100).notNullable();
    table.string('mobile_number', 10).notNullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.boolean('email_verified').notNullable().defaultTo(true);
    table.boolean('account_locked').notNullable().defaultTo(false);
    table.integer('failed_login_attempts').notNullable().defaultTo(0);
    table.timestamp('last_login_at').nullable();
    table.string('social_login_provider', 50).nullable();
    table.string('social_login_provider_id', 255).nullable();
    table.string('social_login_provider_image_url', 255).nullable();
    table.uuid('created_by').nullable();
    table.uuid('updated_by').nullable();
    table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    table.timestamp('updated_on').notNullable().defaultTo(knex.fn.now());
    table.index(['email'], 'idx_users_email');
    table.index(['mobile_number'], 'idx_users_mobile_number');
    table.index(['name'], 'idx_users_name');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('users');
} 
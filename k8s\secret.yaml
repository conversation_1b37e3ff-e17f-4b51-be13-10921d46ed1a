apiVersion: v1
kind: Secret
metadata:
  name: ai-nest-backend-secrets
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: secrets
type: Opaque
data:
  # Database credentials (base64 encoded)
  # Default values - CHANGE THESE IN PRODUCTION
  DB_USER: cG9zdGdyZXM=  # postgres
  DB_PASSWORD: cGFzc3dvcmQ=  # password
  
  # JWT Secret (base64 encoded)
  # Default value - CHANGE THIS IN PRODUCTION
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=  # supersecretkey
  
  # SMTP credentials (base64 encoded)
  # Default values - CHANGE THESE IN PRODUCTION
  SMTP_USER: ****************************************  # <EMAIL>
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==  # fqactehafmzlltzz
  
  # Google OAuth credentials (base64 encoded)
  # Default values - CHAN<PERSON> THESE IN PRODUCTION
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==  # 1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=  # 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

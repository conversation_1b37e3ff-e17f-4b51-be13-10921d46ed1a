import { K<PERSON> } from 'knex';
import { v4 as uuidv4 } from 'uuid';

// Idempotent seed: deletes all and inserts sample users
export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex('users').del();

  // Inserts seed entries
  await knex('users').insert([
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'hashedpassword1',
      name: 'Test User1',
      mobile_number: '**********',
      is_active: true,
      email_verified: true,
      account_locked: false,
      failed_login_attempts: 0,
      last_login_at: null,
      social_login_provider: null,
      social_login_provider_id: null,
      social_login_provider_image_url: null,
      created_by: null,
      updated_by: null,
      created_on: new Date(),
      updated_on: new Date(),
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: 'hashedpassword2',
      name: 'Test User2',
      mobile_number: '**********',
      is_active: true,
      email_verified: false,
      account_locked: false,
      failed_login_attempts: 0,
      last_login_at: null,
      social_login_provider: null,
      social_login_provider_id: null,
      social_login_provider_image_url: null,
      created_by: null,
      updated_by: null,
      created_on: new Date(),
      updated_on: new Date(),
    },
  ]);
} 
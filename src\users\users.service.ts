import { Injectable, Inject } from '@nestjs/common';
import { Knex } from 'knex';

@Injectable()
export class UsersService {
  constructor(@Inject('KNEX_CONNECTION') private readonly knex: Knex) {}

  async findByEmail(email: string) {
    return this.knex('users').where({ email }).first();
  }

  async createUser(userData: any) {
    const [user] = await this.knex('users')
      .insert(userData)
      .returning('*');
    return user;
  }
  // Add more user-related methods as needed
} 
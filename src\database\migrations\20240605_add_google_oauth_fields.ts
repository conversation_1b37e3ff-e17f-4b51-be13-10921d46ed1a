import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    // Add Google OAuth specific fields
    table.string('google_id', 255).nullable().unique();
    table.string('first_name', 100).nullable();
    table.string('last_name', 100).nullable();
    table.string('profile_picture', 500).nullable();
    
    // Add indexes for better performance
    table.index(['google_id'], 'idx_users_google_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    table.dropIndex(['google_id'], 'idx_users_google_id');
    table.dropColumn('google_id');
    table.dropColumn('first_name');
    table.dropColumn('last_name');
    table.dropColumn('profile_picture');
  });
} 
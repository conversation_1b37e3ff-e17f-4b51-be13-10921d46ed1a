apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-nest-backend
  template:
    metadata:
      labels:
        app: ai-nest-backend
        component: api
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:13
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
      containers:
      - name: ai-nest-backend
        image: docker.io/saipriya104/ai-nest-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        # Configuration from ConfigMap
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: PORT
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_NAME
        - name: JWT_EXPIRATION
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: JWT_EXPIRATION
        - name: SMTP_HOST
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: SMTP_HOST
        - name: SMTP_PORT
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: SMTP_PORT
        - name: SMTP_FROM
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: SMTP_FROM
        - name: APP_URL
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: APP_URL
        - name: API_URL
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: API_URL
        - name: CORS_ALLOWED_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: CORS_ALLOWED_ORIGINS
        - name: CORS_ALLOWED_METHODS
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: CORS_ALLOWED_METHODS
        - name: CORS_ALLOW_CREDENTIALS
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: CORS_ALLOW_CREDENTIALS
        - name: CORS_MAX_AGE
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: CORS_MAX_AGE
        - name: OAUTH2_AUTHORIZED_REDIRECT_URIS
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: OAUTH2_AUTHORIZED_REDIRECT_URIS
        - name: GOOGLE_REDIRECT_URI
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: GOOGLE_REDIRECT_URI
        - name: GOOGLE_SCOPE
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: GOOGLE_SCOPE
        # Secrets
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: JWT_SECRET
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        livenessProbe:
          httpGet:
            path: /oauth2/status
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /oauth2/status
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      restartPolicy: Always

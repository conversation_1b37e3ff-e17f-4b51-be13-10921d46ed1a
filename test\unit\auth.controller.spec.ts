import { Test, TestingModule } from '@nestjs/testing';
import { AuthController, OAuth2Controller } from '../../src/auth/auth.controller';
import { AuthService } from '../../src/auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import { HttpStatus } from '@nestjs/common';
import { 
  RegisterDto, 
  CreateUserDto, 
  ResendVerificationDto, 
  ForgotPasswordDto, 
  ResetPasswordDto, 
  LoginDto, 
  LogoutDto 
} from '../../src/auth/dto';
import { NotImplementedException } from '@nestjs/common';
import { Logger } from '@nestjs/common';

// Mock implementation to avoid using the real one
jest.mock('../../src/auth/auth.service');

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        AuthService,
        ConfigService,

      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should call authService.register', async () => {
      const dto = new RegisterDto();
      await controller.register(dto);
      expect(authService.register).toHaveBeenCalledWith(dto);
    });
  });

  describe('verifyEmail', () => {
    it('should call authService.verifyEmail', async () => {
      const token = 'some-token';
      await controller.verifyEmail(token);
      expect(authService.verifyEmail).toHaveBeenCalledWith(token);
    });
  });

  describe('createUser', () => {
    it('should call authService.createUser', async () => {
      const dto = new CreateUserDto();
      await controller.createUser(dto);
      expect(authService.createUser).toHaveBeenCalledWith(dto);
    });
  });

  describe('resendVerification', () => {
    it('should call authService.resendVerification', async () => {
      const dto = new ResendVerificationDto();
      await controller.resendVerification(dto);
      expect(authService.resendVerification).toHaveBeenCalledWith(dto);
    });
  });

  describe('forgotPassword', () => {
    it('should call authService.forgotPassword', async () => {
      const dto = new ForgotPasswordDto();
      await controller.forgotPassword(dto);
      expect(authService.forgotPassword).toHaveBeenCalledWith(dto);
    });
  });

  describe('resetPassword', () => {
    it('should call authService.resetPassword', async () => {
      const dto = new ResetPasswordDto();
      await controller.resetPassword(dto);
      expect(authService.resetPassword).toHaveBeenCalledWith(dto);
    });
  });

  describe('login', () => {
    it('should call authService.login', async () => {
      const dto = new LoginDto();
      await controller.login(dto);
      expect(authService.login).toHaveBeenCalledWith(dto);
    });
  });

  describe('logout', () => {
    it('should call authService.logout', async () => {
      const dto = new LogoutDto();
      const req = { user: { sub: '1' } } as unknown as Request;
      await controller.logout(dto, req);
      expect(authService.logout).toHaveBeenCalledWith(dto, req.user);
    });
  });
});

describe('OAuth2Controller', () => {
  let controller: OAuth2Controller;
  let authService: AuthService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OAuth2Controller],
      providers: [
        AuthService,
        ConfigService,

      ],
    }).compile();

    controller = module.get<OAuth2Controller>(OAuth2Controller);
    authService = module.get<AuthService>(AuthService);
    configService = module.get<ConfigService>(ConfigService);
  });
    
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('googleAuth', () => {
    it('should be a dummy method for guard and swagger', () => {
        expect(() => controller.googleAuth()).toThrow(NotImplementedException);
    });
  });

  describe('getOAuth2Status', () => {
    it('should return the OAuth2 status', async () => {
      jest.spyOn(configService, 'get').mockReturnValue({
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
        redirectUri: 'http://localhost/callback',
        scope: ['email'],
        authorizedRedirectUris: ['http://localhost:3000']
      });

      const result = await controller.getOAuth2Status();
      expect(result.success).toBe(true);
      expect(result.googleOAuth.clientId).toBe('Configured');
    });
  });

  describe('googleAuthCallback', () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Response;

    beforeEach(() => {
        mockRequest = {
            ip: '127.0.0.1',
            headers: { 'user-agent': 'jest-test' },
        };
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
            redirect: jest.fn().mockReturnThis(),
        } as unknown as Response;
    });

    it('should handle successful google auth and return JSON', async () => {
        const googleUser = { email: '<EMAIL>' };
        mockRequest.user = googleUser as any;
        mockRequest.session = { id: 'dummy-session-id', cookie: {} } as any;
        const authResult = {
            accessToken: 'token',
            user: {
                id: '1',
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                picture: 'https://example.com/picture.jpg',
                isEmailVerified: true
            },
            sessionToken: 'session-token',
            refreshToken: 'refresh-token',
            expiresAt: '2024-12-31T23:59:59.000Z',
            refreshExpiresAt: '2024-12-31T23:59:59.000Z'
        };
        jest.spyOn(authService, 'googleAuth').mockResolvedValue(authResult);
        jest.spyOn(configService, 'get').mockImplementation((key: string) => {
          if (key === 'googleOAuth.authorizedRedirectUris') {
            return [
              'http://localhost:8080/oauth2/callback/google',
              'http://localhost:3000/oauth2/redirect',
              'myandroidapp://oauth2/redirect',
              'myiosapp://oauth2/redirect',
            ];
          }
          return undefined;
        });
        await controller.googleAuthCallback(mockRequest as Request, mockResponse);
        expect(authService.googleAuth).toHaveBeenCalledWith(googleUser, '127.0.0.1', 'jest-test');
        expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
        expect(mockResponse.json).toHaveBeenCalledWith({
            success: true,
            message: 'Google authentication successful',
            ...authResult,
        });
    });

    it('should redirect if headers accept html', async () => {
        const googleUser = { email: '<EMAIL>' };
        mockRequest.user = googleUser as any;
        mockRequest.headers = { accept: 'text/html' };
        mockRequest.session = { id: 'dummy-session-id', cookie: {} } as any;
        const authResult = {
            accessToken: 'accessToken',
            user: {
                id: '1',
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                picture: 'https://example.com/picture.jpg',
                isEmailVerified: true
            },
            sessionToken: 'session-token',
            refreshToken: 'refreshToken',
            expiresAt: 'expiresAt',
            refreshExpiresAt: 'refreshExpiresAt'
        };
        jest.spyOn(authService, 'googleAuth').mockResolvedValue(authResult);
        jest.spyOn(configService, 'get').mockImplementation((key: string) => {
          if (key === 'googleOAuth.authorizedRedirectUris') {
            return [
              'http://localhost:8080/oauth2/callback/google',
              'http://localhost:3000/oauth2/redirect',
              'myandroidapp://oauth2/redirect',
              'myiosapp://oauth2/redirect',
            ];
          }
          return undefined;
        });
        await controller.googleAuthCallback(mockRequest as Request, mockResponse);
        expect(mockResponse.redirect).toHaveBeenCalled();
    });

    it('should return 400 if no user from google', async () => {
        mockRequest.user = undefined;
        await controller.googleAuthCallback(mockRequest as Request, mockResponse);
        expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
        expect(mockResponse.json).toHaveBeenCalledWith({
            success: false,
            message: 'No user data received from Google OAuth',
        });
    });

    it('should handle service error', async () => {
        const googleUser = { email: '<EMAIL>' };
        mockRequest.user = googleUser as any;
        const error = new Error('Service Failure');
        jest.spyOn(authService, 'googleAuth').mockRejectedValue(error);

        await controller.googleAuthCallback(mockRequest as Request, mockResponse);

        expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
        expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
            success: false,
            message: 'Google authentication failed',
            error: error.message,
        }));
    });
    
    it('should handle non-error exception', async () => {
        const googleUser = { email: '<EMAIL>' };
        mockRequest.user = googleUser as any;
        jest.spyOn(authService, 'googleAuth').mockRejectedValue('A string error');

        await controller.googleAuthCallback(mockRequest as Request, mockResponse);

        expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
        expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
            success: false,
            message: 'Google authentication failed',
            error: 'Unknown error',
        }));
    });
  });

  describe('debugOAuth2', () => {
    it('should return debug information', async () => {
        jest.spyOn(configService, 'get').mockReturnValue({
            clientId: null,
            clientSecret: 'configured',
            redirectUri: 'uri',
            scope: [],
            authorizedRedirectUris: []
        });

        const result = await controller.debugOAuth2();
        expect(result.success).toBe(true);
        expect(result.message).toBe('OAuth2 Debug Information');
        expect(result.configuration.clientId).toBe('Missing');
        expect(result.configuration.clientSecret).toBe('Configured');
    });
  });

  describe('getEmailStatus', () => {
    it('should return email provider status and recommendations', async () => {
      // Mock the emailService and its getEmailProviderStatus method
      const mockEmailStatus = { provider: 'Gmail', status: 'OK', usage: 100 };
      const mockEmailService = { getEmailProviderStatus: jest.fn().mockReturnValue(mockEmailStatus) };
      jest.spyOn(authService, 'getEmailService').mockReturnValue(mockEmailService as any);

      const result = await controller.getEmailStatus();
      expect(authService.getEmailService).toHaveBeenCalled();
      expect(mockEmailService.getEmailProviderStatus).toHaveBeenCalled();
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Email provider status');
      expect(result).toHaveProperty('emailProviders', mockEmailStatus);
      expect(result).toHaveProperty('recommendations');
      expect(result.recommendations).toHaveProperty('gmailLimit');
    });
  });
});
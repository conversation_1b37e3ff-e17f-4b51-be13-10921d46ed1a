import { registerAs } from '@nestjs/config';

export const databaseConfig = registerAs('database', () => {
  if (process.env.DATABASE_URL) {
    return {
      connectionString: process.env.DATABASE_URL,
    };
  }
  return {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT ?? '5432', 10),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD ?? 'defaultPassword',
    database: process.env.DB_NAME,
  };
}); 
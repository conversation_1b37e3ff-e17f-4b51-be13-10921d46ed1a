apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-nest-backend

resources:
  # Namespace and configuration
  - namespace.yaml
  - configmap.yaml
  - secret.yaml
  
  # Database components
  - postgres-pvc.yaml
  - postgres-deployment.yaml
  - postgres-service.yaml
  
  # Application components
  - deployment.yaml
  - service.yaml
  - ingress.yaml
  
  # Database initialization
  - migration-job.yaml

commonLabels:
  app.kubernetes.io/name: ai-nest-backend
  app.kubernetes.io/version: "1.0.0"
  app.kubernetes.io/managed-by: argocd

images:
  - name: chidhagniconsulting/ai-nest-backend
    newTag: latest

apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend-final
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend-final
    version: final
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-nest-backend-final
  template:
    metadata:
      labels:
        app: ai-nest-backend-final
        version: final
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:13
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          value: "password"
      containers:
      - name: ai-nest-backend
        image: saipriya104/ai-nest-backend:final-fix
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "userauth"
        - name: DB_USER
          value: "postgres"
        - name: DB_PASSWORD
          value: "password"
        - name: JWT_SECRET
          value: "your-super-secret-jwt-key-change-in-production"
        - name: JWT_EXPIRATION
          value: "1h"
        - name: CORS_ALLOWED_ORIGINS
          value: "http://localhost:3000"
        - name: CORS_ALLOWED_METHODS
          value: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
        - name: CORS_ALLOW_CREDENTIALS
          value: "true"
        - name: SMTP_HOST
          value: "smtp.gmail.com"
        - name: SMTP_PORT
          value: "587"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"

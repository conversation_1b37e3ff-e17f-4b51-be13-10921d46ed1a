apiVersion: batch/v1
kind: Job
metadata:
  name: ai-nest-backend-db-init
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: db-init
spec:
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: ai-nest-backend
        component: db-init
    spec:
      restartPolicy: OnFailure
      containers:
      - name: db-init
        image: postgres:13
        command: ['sh', '-c']
        args:
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "Still waiting for PostgreSQL..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
          
          echo "Creating database if not exists..."
          PGPASSWORD=$DB_PASSWORD psql -h postgres-service -p 5432 -U postgres -c "CREATE DATABASE IF NOT EXISTS $DB_NAME;" || true
          
          echo "Database initialization completed!"
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "userauth"
        - name: DB_USER
          value: "postgres"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD

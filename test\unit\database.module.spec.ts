import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseModule } from '../../src/database/database.module';
import { ConfigModule } from '@nestjs/config';

const mockKnex = { raw: jest.fn().mockResolvedValue({ rows: [{ result: 2 }] }) };

describe('DatabaseModule', () => {
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [ConfigModule.forRoot(), DatabaseModule],
    })
      .overrideProvider('KNEX_CONNECTION')
      .useValue(mockKnex)
      .compile();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide KnexProvider', () => {
    const knex = module.get('KNEX_CONNECTION');
    expect(knex).toBeDefined();
  });
}); 
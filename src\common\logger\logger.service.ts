import { Injectable, LoggerService as NestLoggerService, LogLevel } from '@nestjs/common';
import * as winston from 'winston';
import 'winston-daily-rotate-file';

const logFormat = winston.format.printf((info) => {
  const { timestamp, level, message } = info;
  let contextStr = '';
  const hasContext = Object.hasOwn(info, 'context');
  const contextValue = hasContext ? info.context : undefined;
  if (contextValue !== undefined && contextValue !== null) {
    if (typeof contextValue === 'object') {
      try {
        contextStr = JSON.stringify(contextValue);
      } catch {
        contextStr = '[Unserializable Context]';
      }
    } else if (
      typeof contextValue === 'string' ||
      typeof contextValue === 'number' ||
      typeof contextValue === 'boolean'
    ) {
      contextStr = String(contextValue);
    } else {
      contextStr = '[Invalid Context Type]';
    }
  }
  return `${timestamp} [${level}]${contextStr ? ' [' + contextStr + ']' : ''}: ${message}`;
});

const transports: winston.transport[] = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp(),
      logFormat
    ),
  }),
];

if (process.env.NODE_ENV !== 'test') {
  transports.push(
    new winston.transports.DailyRotateFile({
      filename: 'logs/application-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        logFormat
      ),
      level: 'info',
    })
  );
}

export const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  levels: winston.config.npm.levels,
  transports,
});

@Injectable()
export class LoggerService implements NestLoggerService {
  private context?: string;

  setContext(context: string) {
    this.context = context;
  }

  private static safeStringifyContext(context: any): string | undefined {
    if (context === undefined || context === null) return undefined;
    if (typeof context === 'object') {
      try {
        return JSON.stringify(context);
      } catch {
        return '[Unserializable Context]';
      }
    }
    return String(context);
  }

  log(message: any, context?: any) {
    logger.info(message, { context: LoggerService.safeStringifyContext(context ?? this.context) });
  }
  error(message: any, trace?: string, context?: any) {
    logger.error(message + (trace ? `\n${trace}` : ''), { context: LoggerService.safeStringifyContext(context ?? this.context) });
  }
  warn(message: any, context?: any) {
    logger.warn(message, { context: LoggerService.safeStringifyContext(context ?? this.context) });
  }
  debug?(message: any, context?: any) {
    logger.debug(message, { context: LoggerService.safeStringifyContext(context ?? this.context) });
  }
  verbose?(message: any, context?: any) {
    logger.verbose(message, { context: LoggerService.safeStringifyContext(context ?? this.context) });
  }
  setLogLevels?(levels: LogLevel[]) {
    // Optionally implement if needed
  }
} 
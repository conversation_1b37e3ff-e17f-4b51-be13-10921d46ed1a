import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private readonly configService: ConfigService) {
    const googleConfig = configService.get('googleOAuth');
    
    console.log('🔧 Initializing Google Strategy with config:', {
      clientID: googleConfig.clientId ? 'Configured' : 'Missing',
      clientSecret: googleConfig.clientSecret ? 'Configured' : 'Missing',
      callbackURL: googleConfig.redirectUri,
      scope: googleConfig.scope,
    });

    super({
      clientID: googleConfig.clientId,
      clientSecret: googleConfig.clientSecret,
      callbackURL: googleConfig.redirectUri,
      scope: googleConfig.scope.split(',').map((s: string) => s.trim()),
      passReqToCallback: false,
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      console.log('🔧 Google OAuth validate called with profile:', {
        id: profile.id,
        displayName: profile.displayName,
        emails: profile.emails?.length ?? 0,
        photos: profile.photos?.length ?? 0,
      });

      const { name, emails, photos } = profile;
      
      if (!emails || emails.length === 0) {
        console.error('❌ No email found in Google profile');
        return done(new Error('No email found in Google profile'), undefined);
      }

      const user = {
        email: emails[0].value,
        firstName: name?.givenName ?? '',
        lastName: name?.familyName ?? '',
        picture: photos?.[0]?.value ?? '',
        accessToken,
        googleId: profile.id,
      };

      console.log('✅ Google user data prepared:', {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        googleId: user.googleId,
      });
      
      done(null, user);
    } catch (error) {
      console.error('❌ Google OAuth validate error:', error);
      done(error, undefined);
    }
  }
} 
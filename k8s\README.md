# AI Nest Backend Kubernetes Manifests

This directory contains the complete Kubernetes manifests for deploying the AI Nest Backend application using GitOps with Argo CD.

## Architecture Overview

The deployment consists of:
- **NestJS Application**: Authentication backend with JWT and OAuth2
- **PostgreSQL Database**: Persistent data storage
- **ConfigMap**: Non-sensitive configuration
- **Secrets**: Sensitive credentials and API keys
- **Services**: Internal and external networking
- **Ingress**: External access routing
- **Migration Job**: Database initialization

## Manifest Files

### Core Application
- `namespace.yaml` - Dedicated namespace for the application
- `deployment.yaml` - Main application deployment with health checks
- `service.yaml` - NodePort service for external access
- `configmap.yaml` - Application configuration
- `secret.yaml` - Sensitive credentials (base64 encoded)

### Database
- `postgres-deployment.yaml` - PostgreSQL database deployment
- `postgres-service.yaml` - Database service (ClusterIP)
- `postgres-pvc.yaml` - Persistent volume claim for data storage
- `migration-job.yaml` - Database initialization job

### Networking
- `ingress.yaml` - Ingress controller configuration (optional)

### Deployment Management
- `kustomization.yaml` - Kustomize configuration for organized deployment

## Deployment Order

1. **Namespace and Configuration**
   ```bash
   kubectl apply -f namespace.yaml
   kubectl apply -f configmap.yaml
   kubectl apply -f secret.yaml
   ```

2. **Database Components**
   ```bash
   kubectl apply -f postgres-pvc.yaml
   kubectl apply -f postgres-deployment.yaml
   kubectl apply -f postgres-service.yaml
   ```

3. **Application Components**
   ```bash
   kubectl apply -f deployment.yaml
   kubectl apply -f service.yaml
   ```

4. **Database Initialization**
   ```bash
   kubectl apply -f migration-job.yaml
   ```

5. **External Access (Optional)**
   ```bash
   kubectl apply -f ingress.yaml
   ```

## Environment Variables

### ConfigMap Variables
- `NODE_ENV`: Application environment (production)
- `PORT`: Application port (8080)
- `DB_HOST`: Database hostname (postgres-service)
- `DB_PORT`: Database port (5432)
- `DB_NAME`: Database name (userauth)
- CORS, SMTP, OAuth2 configuration

### Secret Variables (Base64 Encoded)
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `JWT_SECRET`: JWT signing secret
- `SMTP_USER`: Email service username
- `SMTP_PASS`: Email service password
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret

## Health Checks

The application includes comprehensive health checks:
- **Liveness Probe**: `/oauth2/status` endpoint
- **Readiness Probe**: `/oauth2/status` endpoint
- **Database Health**: PostgreSQL `pg_isready` checks

## Resource Requirements

### Application Pod
- **Requests**: 512Mi memory, 250m CPU
- **Limits**: 1Gi memory, 500m CPU

### Database Pod
- **Requests**: 256Mi memory, 250m CPU
- **Limits**: 512Mi memory, 500m CPU
- **Storage**: 5Gi persistent volume

## External Access

### NodePort Service
- **Port**: 30080 (external)
- **Target**: 8080 (internal)
- **Access**: `http://<minikube-ip>:30080`

### Minikube Access
```bash
minikube service ai-nest-backend-service -n ai-nest-backend --url
```

## Security Considerations

⚠️ **IMPORTANT**: The default secrets in `secret.yaml` are for development only.

For production deployment:
1. Generate new secure passwords and secrets
2. Update base64 encoded values in `secret.yaml`
3. Use proper secret management (e.g., Sealed Secrets, External Secrets)
4. Configure proper RBAC and network policies

## Troubleshooting

### Check Pod Status
```bash
kubectl get pods -n ai-nest-backend
kubectl describe pod <pod-name> -n ai-nest-backend
```

### Check Logs
```bash
kubectl logs -f deployment/ai-nest-backend -n ai-nest-backend
kubectl logs -f deployment/postgres -n ai-nest-backend
```

### Check Services
```bash
kubectl get svc -n ai-nest-backend
kubectl describe svc ai-nest-backend-service -n ai-nest-backend
```

### Database Connection Test
```bash
kubectl exec -it deployment/postgres -n ai-nest-backend -- psql -U postgres -d userauth
```

## GitOps with Argo CD

This manifest set is designed for GitOps deployment with Argo CD:
- All resources are properly labeled and namespaced
- Deployment order is managed through dependencies
- Health checks ensure proper startup sequence
- Configuration is externalized for easy management

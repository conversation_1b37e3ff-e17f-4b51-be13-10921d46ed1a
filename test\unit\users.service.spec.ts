// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { UsersService } from '../../src/users/users.service';

describe('UsersService', () => {
  let service: UsersService;
  let knexMock: any;

  beforeEach(() => {
    knexMock = jest.fn(() => knexMock);
    knexMock.where = jest.fn().mockReturnThis();
    knexMock.first = jest.fn();
    knexMock.insert = jest.fn().mockReturnThis();
    knexMock.returning = jest.fn();
    service = new UsersService(knexMock as any);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('findByEmail should query users table', async () => {
    knexMock.where.mockReturnThis();
    knexMock.first.mockResolvedValue({ id: 1, email: '<EMAIL>' });
    const result = await service.findByEmail('<EMAIL>');
    expect(knexMock.where).toHaveBeenCalledWith({ email: '<EMAIL>' });
    expect(result).toEqual({ id: 1, email: '<EMAIL>' });
  });

  it('createUser should insert and return user', async () => {
    knexMock.insert.mockReturnThis();
    knexMock.returning.mockResolvedValue([{ id: 2, email: '<EMAIL>' }]);
    const result = await service.createUser({ email: '<EMAIL>' });
    expect(knexMock.insert).toHaveBeenCalledWith({ email: '<EMAIL>' });
    expect(result).toEqual({ id: 2, email: '<EMAIL>' });
  });

  it.each([
    [{ email: 'not-an-email' }],
    [{}],
  ])('should throw for invalid or empty input: %p', async (input) => {
    await expect(service.createUser(input)).rejects.toThrow();
  });

  it('should throw for duplicate email', async () => {
    knexMock.where.mockReturnThis();
    knexMock.first.mockResolvedValue({ id: 1, email: '<EMAIL>' });
    await expect(service.createUser({ email: '<EMAIL>' })).rejects.toThrow();
  });

  it('should throw for SQL error', async () => {
    knexMock.insert.mockImplementation(() => { throw new Error('SQL error'); });
    await expect(service.createUser({ email: '<EMAIL>' })).rejects.toThrow('SQL error');
  });

  it('should throw if DB connection fails during findByEmail', async () => {
    knexMock.where.mockImplementationOnce(() => { throw new Error('DB connection lost'); });
    await expect(service.findByEmail('<EMAIL>')).rejects.toThrow('DB connection lost');
  });

  it.each([
    [() => { throw new Error('Transaction failed'); }, 'Transaction failed'],
    [() => { throw new Error('Unexpected error'); }, 'Unexpected error'],
  ])('should throw for DB/transaction/unexpected errors', async (mockImpl, expectedError) => {
    knexMock.insert.mockImplementationOnce(mockImpl);
    await expect(service.createUser({ email: '<EMAIL>' })).rejects.toThrow(expectedError);
  });

  it('should timeout if DB is slow in createUser', async () => {
    jest.useFakeTimers();
    knexMock.insert.mockImplementation(() => ({
      returning: () => new Promise(resolve => setTimeout(() => resolve([{ id: 3, email: '<EMAIL>' }]), 2000))
    }));
    const createPromise = service.createUser({ email: '<EMAIL>' });
    jest.advanceTimersByTime(2000);
    await expect(createPromise).resolves.toEqual({ id: 3, email: '<EMAIL>' });
    jest.useRealTimers();
  });
}); 
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { databaseConfig } from './config/database.config';
import { emailConfig } from './config/email.config';
import { corsConfig } from './config/cors.config';
import googleOAuthConfig from './config/google-oauth.config';
import { DatabaseModule } from './database/database.module';
import { EmailModule } from './email/email.module';
import { LoggerModule } from './common/logger/logger.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV ?? 'development'}`,
      load: [databaseConfig, emailConfig, corsConfig, googleOAuthConfig],
    }),
    DatabaseModule,
    UsersModule,
    AuthModule,
    EmailModule,
    LoggerModule,
  ],
})
export class AppModule {} 
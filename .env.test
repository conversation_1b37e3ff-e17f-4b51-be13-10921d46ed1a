# .env.test
# This file contains environment variables exclusively for the test environment.
# It is loaded by the Jest setup file before any tests are run.

# Node environment
NODE_ENV=test
LOG_LEVEL=error

# JWT Configuration
JWT_SECRET=supersecretkeyfortesting
JWT_EXPIRATION=86400000

# App Configuration
APP_URL=http://localhost:3000
PORT=8080

# Google OAuth - Dummy values for testing
GOOGLE_CLIENT_ID=dummy-google-client-id
GOOGLE_CLIENT_SECRET=dummy-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google
GOOGLE_SCOPE=email,profile,openid

# OAuth2 Configuration
OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:3000/oauth2/redirect,myandroidapp://oauth2/redirect,myiosapp://oauth2/redirect

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Test Database (will be overridden by testcontainers in integration tests)
DB_HOST=localhost
DB_PORT=5433
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=userauth

# Email SMTP configuration (test mode)
EMAIL_PROVIDER=test
SMTP_HOST=smtp.test.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=testpassword
SMTP_FROM='"No Reply" <EMAIL>'
import { Test, TestingModule } from '@nestjs/testing';
import { EmailModule } from '../../src/email/email.module';
import { EmailService } from '../../src/email/email.service';
import { ConfigModule } from '@nestjs/config';
import { MailerService } from '@nestjs-modules/mailer';

describe('EmailModule', () => {
  let module: TestingModule;

  const mockMailerService = {
    sendMail: jest.fn().mockResolvedValue(undefined),
  };

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [ConfigModule.forRoot(), EmailModule],
    })
      .overrideProvider(MailerService)
      .useValue(mockMailerService)
      .compile();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide EmailService', () => {
    const service = module.get<EmailService>(EmailService);
    expect(service).toBeDefined();
  });
}); 
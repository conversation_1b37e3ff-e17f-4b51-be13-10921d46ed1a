import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger, RequestMethod } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AllExceptionsFilter } from './all-exceptions.filter';
import session from 'express-session';

async function setupSwagger(app: any) {
  try {
    Logger.log('Setting up Swagger documentation...', 'Bootstrap');
    
    const config = new DocumentBuilder()
      .setTitle('Chidhagni Auth API')
      .setDescription('Link-based email verification and password reset API')
      .setVersion('2.0.1')
      .addBearerAuth({ type: 'http', scheme: 'bearer', bearerFormat: 'JWT' }, 'bearerAuth')
      .addServer('http://localhost:8080', 'Local development server')
      .build();
    
    const document = SwaggerModule.createDocument(app, config);
    
    // Setup Swagger UI with custom options
    SwaggerModule.setup('api-docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
      },
      customSiteTitle: 'Chidhagni Auth API Documentation',
    });
    
    Logger.log('Swagger documentation setup completed successfully', 'Bootstrap');
    return true;
  } catch (error) {
    Logger.error(`Failed to setup Swagger documentation: ${error instanceof Error ? error.message : String(error)}`, 'Bootstrap');
    Logger.warn('Continuing without Swagger documentation', 'Bootstrap');
    return false;
  }
}

async function bootstrap() {
  const envPath = path.resolve(process.cwd(), '.env');
  Logger.log(`Checking for .env file at: ${envPath}`, 'Bootstrap');
  if (fs.existsSync(envPath)) {
    Logger.log('.env file found!', 'Bootstrap');
  } else {
    Logger.error('.env file NOT FOUND!', 'Bootstrap');
  }

  const app = await NestFactory.create<NestExpressApplication>(AppModule, { logger: ['debug', 'error', 'warn', 'log', 'verbose'] });
  
  app.setGlobalPrefix('api/v1', {
    exclude: [
      { path: 'oauth2/authorize/google', method: RequestMethod.GET },
      { path: 'oauth2/callback/google', method: RequestMethod.GET },
    ],
  });
  
  const configService = app.get(ConfigService);
  
  // Enable CORS with configuration from config service
  const corsConfig = configService.get('cors');
  app.enableCors({
    origin: corsConfig.allowedOrigins,
    methods: corsConfig.allowedMethods,
    credentials: corsConfig.allowCredentials,
    maxAge: corsConfig.maxAge,
  });
  
  Logger.log(`CORS configured with origins: ${corsConfig.allowedOrigins.join(', ')}`, 'Bootstrap');
  
  // Serve static files from public directory
  app.useStaticAssets(path.join(__dirname, '..', 'public'), {
    prefix: '/',
  });
  
  Logger.log('Static files configured from public directory', 'Bootstrap');
  
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }));
  app.useGlobalFilters(new AllExceptionsFilter());

  // Enable session middleware for OAuth2 dynamic redirect_uri support
  app.use(
    session({
      secret: 'your-session-secret', // Change this to a strong secret in production!
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === 'production', // Only send cookie over HTTPS in production
        httpOnly: true, // Prevent client-side JS from accessing the cookie
        sameSite: 'lax', // Helps prevent CSRF
      },
    })
  );

  const port = configService.get<number>('PORT', 8080);

  Logger.log(`Starting application on port ${port}`, 'Bootstrap');

  // Setup Swagger before app.init()
  if (process.env.NODE_ENV === 'development') {
    await setupSwagger(app);
  }

  // Initialize the application
  await app.init();
  Logger.log('Application initialized successfully', 'Bootstrap');

  // Start listening
  await app.listen(port);
  Logger.log(`NestJS app running on port ${port}`, 'Bootstrap');
  Logger.log(`Swagger docs available at http://localhost:${port}/api-docs`, 'Bootstrap');
  Logger.log(`API endpoints available at http://localhost:${port}/api/v1`, 'Bootstrap');
  Logger.log(`OAuth debug page available at http://localhost:${port}/oauth-debug.html`, 'Bootstrap');
}

bootstrap().catch((error) => {
  Logger.error(`Failed to start application: ${error.message}`, 'Bootstrap');
  if (process.env.NODE_ENV === 'development') {
    process.exit(1);
  }
});

export { bootstrap }; 
import * as DTOs from '../../src/auth/dto';
import { validate } from 'class-validator';

describe('Auth DTOs', () => {
  Object.entries(DTOs).forEach(([name, DTO]) => {
    it(`should instantiate ${name}`, () => {
      expect(() => new (DTO as any)()).not.toThrow();
    });
  });

  it('ForgotPasswordDto should require valid email, ip_address, and device_details', async () => {
    const { ForgotPasswordDto } = DTOs;
    const dto = new ForgotPasswordDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.email = 'not-an-email';
    dto.ipAddress = 123 as any;
    dto.deviceDetails = 456 as any;
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'email')).toBe(true);
    expect(errors.some(e => e.property === 'ipAddress')).toBe(true);
    expect(errors.some(e => e.property === 'deviceDetails')).toBe(true);
    dto.email = '<EMAIL>';
    dto.ipAddress = '127.0.0.1';
    dto.deviceDetails = 'test';
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('RegisterDto should require valid email, name, and mobile_number', async () => {
    const { RegisterDto } = DTOs;
    const dto = new RegisterDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.email = 'not-an-email';
    dto.name = '';
    dto.mobileNumber = '123';
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'email')).toBe(true);
    expect(errors.some(e => e.property === 'name')).toBe(true);
    expect(errors.some(e => e.property === 'mobileNumber')).toBe(true);
    dto.email = '<EMAIL>';
    dto.name = 'A';
    dto.mobileNumber = '1234567890';
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('CreateUserDto should require valid email, password, ip_address, and device_details', async () => {
    const { CreateUserDto } = DTOs;
    const dto = new CreateUserDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.email = 'not-an-email';
    dto.password = 'short';
    dto.ipAddress = '';
    dto.deviceDetails = '';
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'email')).toBe(true);
    expect(errors.some(e => e.property === 'password')).toBe(true);
    expect(errors.some(e => e.property === 'ipAddress')).toBe(true);
    expect(errors.some(e => e.property === 'deviceDetails')).toBe(true);
    dto.email = '<EMAIL>';
    dto.password = 'longenoughpassword';
    dto.ipAddress = '127.0.0.1';
    dto.deviceDetails = 'test';
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('ResendVerificationDto should require valid email', async () => {
    const { ResendVerificationDto } = DTOs;
    const dto = new ResendVerificationDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.email = 'not-an-email';
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'email')).toBe(true);
    dto.email = '<EMAIL>';
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('ResetPasswordDto should require valid reset_token, new_password, ip_address, and device_details', async () => {
    const { ResetPasswordDto } = DTOs;
    const dto = new ResetPasswordDto();
    dto.resetToken = '';
    dto.newPassword = '';
    dto.ipAddress = '';
    dto.deviceDetails = '';
    const errors = await validate(dto);
    expect(errors.some(e => e.property === 'resetToken')).toBe(true);
    expect(errors.some(e => e.property === 'newPassword')).toBe(true);
    expect(errors.some(e => e.property === 'ipAddress')).toBe(true);
    expect(errors.some(e => e.property === 'deviceDetails')).toBe(true);
  });

  it('LoginDto should require valid email, password, ip_address, device_details, and overrideExistingLogins', async () => {
    const { LoginDto } = DTOs;
    const dto = new LoginDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.email = 'not-an-email';
    dto.password = '';
    dto.ipAddress = '';
    dto.deviceDetails = '';
    dto.overrideExistingLogins = 'not-a-boolean' as any;
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'email')).toBe(true);
    expect(errors.some(e => e.property === 'password')).toBe(true);
    expect(errors.some(e => e.property === 'ipAddress')).toBe(true);
    expect(errors.some(e => e.property === 'deviceDetails')).toBe(true);
    expect(errors.some(e => e.property === 'overrideExistingLogins')).toBe(true);
    dto.email = '<EMAIL>';
    dto.password = 'password123';
    dto.ipAddress = '127.0.0.1';
    dto.deviceDetails = 'test';
    dto.overrideExistingLogins = false;
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('LogoutDto should require valid session_token, ip_address, and device_details', async () => {
    const { LogoutDto } = DTOs;
    const dto = new LogoutDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.sessionToken = '';
    dto.ipAddress = '';
    dto.deviceDetails = '';
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'sessionToken')).toBe(true);
    expect(errors.some(e => e.property === 'ipAddress')).toBe(true);
    expect(errors.some(e => e.property === 'deviceDetails')).toBe(true);
    dto.sessionToken = 'token';
    dto.ipAddress = '127.0.0.1';
    dto.deviceDetails = 'test';
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('GoogleCallbackDto should require valid code and optional state', async () => {
    const { GoogleCallbackDto } = DTOs;
    const dto = new GoogleCallbackDto();
    let errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    dto.code = '';
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'code')).toBe(true);
    dto.code = 'validcode';
    dto.state = 123 as any;
    errors = await validate(dto);
    expect(errors.some(e => e.property === 'state')).toBe(true);
    dto.state = 'validstate';
    errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  // Add similar validation tests for other DTOs as needed
}); 
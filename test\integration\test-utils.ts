import { PostgreSqlContainer } from '@testcontainers/postgresql';
import { StartedTestContainer } from 'testcontainers';
import knex, { Knex } from 'knex';
import path from 'path';

export interface TestDbContext {
  container: StartedTestContainer;
  knex: Knex;
}

export async function setupTestDb(): Promise<TestDbContext> {
  const container = await new PostgreSqlContainer('postgres:15')
    .withDatabase('test_db')
    .withUsername('test_user')
    .withPassword('test_password')
    .start();

  const db = knex({
    client: 'pg',
    connection: {
      host: container.getHost(),
      port: container.getPort(),
      user: container.getUsername(),
      password: container.getPassword(),
      database: container.getDatabase(),
    },
    migrations: {
      directory: path.join(__dirname, '../../src/database/migrations'),
    },
  });

  // Wait for DB to be ready
  let ready = false;
  for (let i = 0; i < 10; i++) {
    try {
      await db.raw('SELECT 1');
      ready = true;
      break;
    } catch (e) {
      await new Promise((res) => setTimeout(res, 1000));
    }
  }
  if (!ready) throw new Error('Test DB did not become ready');

  // Run migrations
  await db.migrate.latest();
  // Optionally run seeds if you have them
  await seedDatabase(db);

  return { container, knex: db };
}

export async function teardownTestDb(ctx: TestDbContext | undefined) {
  if (!ctx) return;
  if (ctx.knex) await ctx.knex.destroy();
  if (ctx.container) await ctx.container.stop();
}

export async function cleanDb(knex: Knex) {
  // Truncate all tables (Postgres only)
  const tables = await knex.raw(`
    SELECT tablename FROM pg_tables WHERE schemaname = 'public';
  `);
  for (const row of tables.rows) {
    await knex.raw(`TRUNCATE TABLE "${row.tablename}" RESTART IDENTITY CASCADE;`);
  }
}

export async function seedDatabase(knex: Knex) {
  // Run all seeds in the seeds/ folder before tests
  await knex.seed.run({ directory: path.resolve(__dirname, '../../seeds') });
} 
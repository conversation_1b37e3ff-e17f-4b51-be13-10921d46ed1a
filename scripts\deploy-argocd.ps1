# AI Nest Backend - Argo CD Deployment Script
# This script sets up the GitOps pipeline using Argo CD

Write-Host "🚀 AI Nest Backend - GitOps Deployment with Argo CD" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if kubectl is available
try {
    kubectl version --client --short | Out-Null
    Write-Host "✅ kubectl is available" -ForegroundColor Green
} catch {
    Write-Host "❌ kubectl is not available. Please install kubectl first." -ForegroundColor Red
    exit 1
}

# Check if Argo CD is running
Write-Host "`n🔍 Checking Argo CD installation..." -ForegroundColor Yellow
try {
    $argoCDPods = kubectl get pods -n argocd --no-headers 2>$null
    if ($argoCDPods) {
        Write-Host "✅ Argo CD is running" -ForegroundColor Green
    } else {
        Write-Host "❌ Argo CD is not running. Please install Argo CD first." -ForegroundColor Red
        Write-Host "Install with: kubectl create namespace argocd && kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Error checking Argo CD status" -ForegroundColor Red
    exit 1
}

# Apply Argo CD Project
Write-Host "`n📋 Creating Argo CD Project..." -ForegroundColor Yellow
try {
    kubectl apply -f argocd/project.yaml
    Write-Host "✅ Argo CD Project created successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create Argo CD Project" -ForegroundColor Red
    exit 1
}

# Apply Argo CD Application
Write-Host "`n🎯 Creating Argo CD Application..." -ForegroundColor Yellow
try {
    kubectl apply -f argocd/application.yaml
    Write-Host "✅ Argo CD Application created successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create Argo CD Application" -ForegroundColor Red
    exit 1
}

# Wait for application to be created
Write-Host "`n⏳ Waiting for application to be recognized by Argo CD..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check application status
Write-Host "`n📊 Checking application status..." -ForegroundColor Yellow
try {
    $appStatus = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.sync.status}' 2>$null
    if ($appStatus) {
        Write-Host "✅ Application status: $appStatus" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Application status not yet available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not retrieve application status" -ForegroundColor Yellow
}

# Get Argo CD server URL
Write-Host "`n🌐 Getting Argo CD server access information..." -ForegroundColor Yellow
try {
    # Check if there's a NodePort service
    $nodePortService = kubectl get svc argocd-server -n argocd -o jsonpath='{.spec.type}' 2>$null
    
    if ($nodePortService -eq "NodePort") {
        $nodePort = kubectl get svc argocd-server -n argocd -o jsonpath='{.spec.ports[0].nodePort}'
        $minikubeIP = minikube ip 2>$null
        if ($minikubeIP) {
            Write-Host "🔗 Argo CD UI: http://$minikubeIP`:$nodePort" -ForegroundColor Cyan
        }
    } else {
        Write-Host "🔗 Use port-forward to access Argo CD:" -ForegroundColor Cyan
        Write-Host "   kubectl port-forward svc/argocd-server -n argocd 8080:443" -ForegroundColor White
        Write-Host "   Then access: https://localhost:8080" -ForegroundColor White
    }
} catch {
    Write-Host "⚠️  Could not determine Argo CD access method" -ForegroundColor Yellow
    Write-Host "   Try: kubectl port-forward svc/argocd-server -n argocd 8080:443" -ForegroundColor White
}

# Get initial admin password
Write-Host "`n🔑 Getting Argo CD admin password..." -ForegroundColor Yellow
try {
    $adminPassword = kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" 2>$null
    if ($adminPassword) {
        $decodedPassword = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($adminPassword))
        Write-Host "👤 Username: admin" -ForegroundColor Cyan
        Write-Host "🔐 Password: $decodedPassword" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️  Could not retrieve admin password" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Error retrieving admin password" -ForegroundColor Yellow
}

Write-Host "`n🎉 GitOps deployment setup completed!" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Access Argo CD UI using the information above" -ForegroundColor White
Write-Host "2. Login with admin credentials" -ForegroundColor White
Write-Host "3. Navigate to the 'ai-nest-backend' application" -ForegroundColor White
Write-Host "4. Click 'Sync' to deploy the application" -ForegroundColor White
Write-Host "5. Monitor the deployment progress" -ForegroundColor White
Write-Host "`n📚 For troubleshooting, check: k8s/README.md" -ForegroundColor Cyan

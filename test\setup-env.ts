import { config } from 'dotenv';
import * as path from 'path';

// Load environment variables from .env.test file
// This ensures that all tests run with a consistent, isolated test environment.
config({ path: path.resolve(__dirname, '../../.env.test') });

// Set CI-specific environment variables
if (process.env.CI_ENVIRONMENT === 'true') {
  console.log('🔧 CI environment detected, setting CI-specific configurations...');

  // Set test environment variables for CI
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise in CI

  // Set reasonable defaults for missing environment variables
  if (!process.env.JWT_SECRET) {
    process.env.JWT_SECRET = 'test-jwt-secret-for-ci';
  }

  if (!process.env.APP_URL) {
    process.env.APP_URL = 'http://localhost:3000';
  }

  if (!process.env.GOOGLE_CLIENT_ID) {
    process.env.GOOGLE_CLIENT_ID = 'test-client-id';
  }

  if (!process.env.GOOGLE_CLIENT_SECRET) {
    process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
  }

  console.log('✅ CI environment configuration completed');
}
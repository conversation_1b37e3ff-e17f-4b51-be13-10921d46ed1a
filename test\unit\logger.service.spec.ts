import { LoggerService, logger } from '../../src/common/logger/logger.service';
import * as winston from 'winston';

describe('LoggerService', () => {
  let loggerService: LoggerService;
  let winstonInfoSpy: jest.SpyInstance;
  let winstonErrorSpy: jest.SpyInstance;
  let winstonWarnSpy: jest.SpyInstance;
  let winstonDebugSpy: jest.SpyInstance;
  let winstonVerboseSpy: jest.SpyInstance;

  beforeAll(() => {
    // Spy on logger instance methods
    winstonInfoSpy = jest.spyOn(logger, 'info').mockImplementation(() => ({} as any));
    winstonErrorSpy = jest.spyOn(logger, 'error').mockImplementation(() => ({} as any));
    winstonWarnSpy = jest.spyOn(logger, 'warn').mockImplementation(() => ({} as any));
    winstonDebugSpy = jest.spyOn(logger, 'debug').mockImplementation(() => ({} as any));
    winstonVerboseSpy = jest.spyOn(logger, 'verbose').mockImplementation(() => ({} as any));
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  beforeEach(() => {
    loggerService = new LoggerService();
  });

  it('should set context', () => {
    loggerService.setContext('TestContext');
    // @ts-ignore
    expect(loggerService.context).toBe('TestContext');
  });

  describe('safeStringifyContext', () => {
    it('should return undefined for undefined/null', () => {
      // @ts-ignore
      expect(LoggerService.safeStringifyContext(undefined)).toBeUndefined();
      // @ts-ignore
      expect(LoggerService.safeStringifyContext(null)).toBeUndefined();
    });
    it('should stringify objects', () => {
      // @ts-ignore
      expect(LoggerService.safeStringifyContext({ a: 1 })).toBe(JSON.stringify({ a: 1 }));
    });
    it('should handle unserializable objects', () => {
      const circular: any = {};
      circular.self = circular;
      // @ts-ignore
      expect(LoggerService.safeStringifyContext(circular)).toBe('[Unserializable Context]');
    });
    it('should stringify primitives', () => {
      // @ts-ignore
      expect(LoggerService.safeStringifyContext('str')).toBe('str');
      // @ts-ignore
      expect(LoggerService.safeStringifyContext(123)).toBe('123');
      // @ts-ignore
      expect(LoggerService.safeStringifyContext(true)).toBe('true');
    });
  });

  describe('log methods', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should call winston.info with message and context', () => {
      loggerService.log('info message', 'ctx');
      // No error thrown
    });
    it('should call winston.error with message, trace, and context', () => {
      loggerService.error('error message', 'trace', 'ctx');
      // No error thrown
    });
    it('should call winston.warn with message and context', () => {
      loggerService.warn('warn message', 'ctx');
      // No error thrown
    });
    it('should call winston.debug with message and context', () => {
      loggerService.debug && loggerService.debug('debug message', 'ctx');
      // No error thrown
    });
    it('should call winston.verbose with message and context', () => {
      loggerService.verbose && loggerService.verbose('verbose message', 'ctx');
      // No error thrown
    });
    it('should use instance context if no context is provided', () => {
      loggerService.setContext('InstanceContext');
      loggerService.log('msg');
      loggerService.error('err', undefined);
      loggerService.warn('warn');
      loggerService.debug && loggerService.debug('debug');
      loggerService.verbose && loggerService.verbose('verbose');
    });
    it('should handle undefined context gracefully', () => {
      loggerService.log('msg', undefined);
      loggerService.error('err', undefined, undefined);
      loggerService.warn('warn', undefined);
      loggerService.debug && loggerService.debug('debug', undefined);
      loggerService.verbose && loggerService.verbose('verbose', undefined);
    });
    it('should handle setLogLevels (noop)', () => {
      expect(() => loggerService.setLogLevels && loggerService.setLogLevels(['log', 'error'])).not.toThrow();
    });
  });
}); 
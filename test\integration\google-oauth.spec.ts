import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from '../../src/auth/auth.service';
import { GoogleStrategy } from '../../src/auth/google.strategy';
import { EmailService } from '../../src/email/email.service';
import { setupTestDb, teardownTestDb, cleanDb, TestDbContext } from './test-utils';
import knex, { Knex } from 'knex';
import { v4 as uuidv4 } from 'uuid';
import { Logger } from '@nestjs/common';

jest.setTimeout(120000);

describe('Google OAuth (Integration)', () => {
  let authService: AuthService;
  let googleStrategy: GoogleStrategy;
  let configService: ConfigService;
  let dbCtx: TestDbContext;
  let realKnex: Knex;

  beforeAll(async () => {
    dbCtx = await setupTestDb();
    realKnex = dbCtx.knex;
  });

  afterAll(async () => {
    await teardownTestDb(dbCtx);
  });

  beforeEach(async () => {
    await cleanDb(realKnex);
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        GoogleStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'googleOAuth') {
                return {
                  clientId: 'test-client-id',
                  clientSecret: 'test-client-secret',
                  redirectUri: 'http://localhost:8080/oauth2/callback/google',
                  scope: 'email,profile,openid',
                };
              }
              const config: Record<string, string> = {
                'JWT_SECRET': 'test-jwt-secret',
                'JWT_EXPIRATION': '86400000',
              };
              return config[key];
            }),
          },
        },
        {
          provide: JwtService,
          useValue: { sign: jest.fn().mockReturnValue('mock-jwt-token') },
        },
        {
          provide: EmailService,
          useValue: { sendVerificationLink: jest.fn() },
        },
        {
          provide: 'KNEX_CONNECTION',
          useValue: realKnex,
        },

      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    googleStrategy = module.get<GoogleStrategy>(GoogleStrategy);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GoogleStrategy', () => {
    it('should be defined', () => {
      expect(googleStrategy).toBeDefined();
    });

    it('should validate Google profile correctly', async () => {
      const mockProfile = {
        id: 'google-user-id',
        name: {
          givenName: 'John',
          familyName: 'Doe',
        },
        emails: [{ value: '<EMAIL>' }],
        photos: [{ value: 'https://example.com/photo.jpg' }],
      };

      const mockAccessToken = 'mock-access-token';
      const mockRefreshToken = 'mock-refresh-token';
      const mockDone = jest.fn();

      await googleStrategy.validate(mockAccessToken, mockRefreshToken, mockProfile, mockDone);

      expect(mockDone).toHaveBeenCalledWith(null, {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        picture: 'https://example.com/photo.jpg',
        accessToken: 'mock-access-token',
        googleId: 'google-user-id',
      });
    });

    it('should handle missing email in profile', async () => {
      const mockProfile = {
        id: 'google-user-id',
        name: {
          givenName: 'John',
          familyName: 'Doe',
        },
        emails: [],
        photos: [{ value: 'https://example.com/photo.jpg' }],
      };

      const mockAccessToken = 'mock-access-token';
      const mockRefreshToken = 'mock-refresh-token';
      const mockDone = jest.fn();

      await googleStrategy.validate(mockAccessToken, mockRefreshToken, mockProfile, mockDone);

      expect(mockDone).toHaveBeenCalledWith(expect.any(Error), undefined);
    });
  });

  describe('AuthService.googleAuth', () => {
    it('should create new user for first-time Google login', async () => {
      const mockGoogleUser = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        picture: 'https://example.com/photo.jpg',
        googleId: 'new-google-id',
      };
      // No user exists in DB, so just call the method
      const result = await authService.googleAuth(mockGoogleUser, '127.0.0.1', 'test-device');
      // Assert user is created in DB
      const user = await realKnex('users').where({ email: mockGoogleUser.email }).first();
      expect(user).toBeDefined();
      expect(result.accessToken).toBe('mock-jwt-token');
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.user.firstName).toBe('New');
      expect(result.user.lastName).toBe('User');
      expect(result.user.isEmailVerified).toBe(true);
    });

    it('should update existing user with Google information', async () => {
      // Insert a user with the same email but no Google ID
      const userId = uuidv4();
      await realKnex('users').insert({
        id: userId,
        email: '<EMAIL>',
        name: 'Old Name',
        first_name: 'Old',
        last_name: 'Name',
        profile_picture: 'https://example.com/old-photo.jpg',
        email_verified: false,
        password: 'irrelevant',
        is_active: true,
        created_on: new Date(),
        updated_on: new Date(),
        mobile_number: '1234567890',
      });
      const mockGoogleUser = {
        email: '<EMAIL>',
        firstName: 'Updated',
        lastName: 'Name',
        picture: 'https://example.com/new-photo.jpg',
        googleId: 'existing-google-id',
      };
      const result = await authService.googleAuth(mockGoogleUser, '127.0.0.1', 'test-device');
      // Assert user is updated in DB
      const user = await realKnex('users').where({ email: mockGoogleUser.email }).first();
      expect(user.google_id).toBe('existing-google-id');
      expect(user.first_name).toBe('Updated');
      expect(user.profile_picture).toBe('https://example.com/new-photo.jpg');
      expect(user.email_verified).toBe(true);
      expect(result.user.email).toBe('<EMAIL>');
    });

    it('should login existing user by Google ID', async () => {
      // Insert a user with Google ID
      await realKnex('users').insert({
        id: uuidv4(),
        email: '<EMAIL>',
        name: 'John Doe',
        first_name: 'John',
        last_name: 'Doe',
        profile_picture: 'https://example.com/photo.jpg',
        email_verified: true,
        password: 'irrelevant',
        is_active: true,
        created_on: new Date(),
        updated_on: new Date(),
        mobile_number: '9876543210',
        google_id: 'google-user-id',
      });
      const mockGoogleUser = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        picture: 'https://example.com/photo.jpg',
        googleId: 'google-user-id',
      };
      const result = await authService.googleAuth(mockGoogleUser, '127.0.0.1', 'test-device');
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.user.isEmailVerified).toBe(true);
    });

    it('should create new user with mobile_number', async () => {
      const mockGoogleUser = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        picture: 'https://example.com/photo.jpg',
        googleId: 'google-user-id',
      };
      // No user exists in DB, so just call the method
      const result = await authService.googleAuth(mockGoogleUser, '127.0.0.1', 'test-device');
      // Assert user is created in DB
      const user = await realKnex('users').where({ email: mockGoogleUser.email }).first();
      expect(user).toBeDefined();
      expect(result.accessToken).toBe('mock-jwt-token');
      expect(result.user.email).toBe('<EMAIL>');
      expect(result.user.firstName).toBe('John');
      expect(result.user.lastName).toBe('Doe');
      expect(result.user.isEmailVerified).toBe(true);
      // The service sets mobile_number to '' (empty string) by default for Google users
      expect(user.mobile_number).toBe("");
    });
  });
}); 
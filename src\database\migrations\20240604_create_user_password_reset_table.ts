import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('user_password_reset', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users');
    table.string('reset_token', 255).notNullable().unique();
    table.timestamp('expires_at').notNullable();
    table.boolean('used').notNullable().defaultTo(false);
    table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    table.timestamp('used_at').nullable();
    table.string('ip_address', 15).nullable();
    table.text('device_details').nullable();
  });
  await knex.raw(`CREATE INDEX idx_password_reset_expires_at ON user_password_reset(expires_at)`);
  await knex.raw(`CREATE INDEX idx_password_reset_active ON user_password_reset(user_id, reset_token) WHERE used = false`);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('user_password_reset');
} 
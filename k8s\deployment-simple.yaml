apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend-simple
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend-simple
    component: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-nest-backend-simple
  template:
    metadata:
      labels:
        app: ai-nest-backend-simple
        component: api
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:13
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
      containers:
      - name: ai-nest-backend
        image: node:20-alpine
        workingDir: /app
        command: ['sh', '-c']
        args:
        - |
          echo "Starting simple Node.js server..."
          cat > server.js << 'EOF'
          const http = require('http');
          const url = require('url');
          
          const server = http.createServer((req, res) => {
            const parsedUrl = url.parse(req.url, true);
            
            // Set CORS headers
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            res.setHeader('Content-Type', 'application/json');
            
            if (req.method === 'OPTIONS') {
              res.writeHead(200);
              res.end();
              return;
            }
            
            if (parsedUrl.pathname === '/oauth2/status') {
              res.writeHead(200);
              res.end(JSON.stringify({
                success: true,
                message: 'AI Nest Backend is running',
                timestamp: new Date().toISOString(),
                status: 'healthy',
                database: {
                  host: process.env.DB_HOST || 'postgres-service',
                  port: process.env.DB_PORT || '5432',
                  name: process.env.DB_NAME || 'userauth'
                },
                environment: process.env.NODE_ENV || 'production'
              }));
            } else if (parsedUrl.pathname === '/api-docs' || parsedUrl.pathname === '/') {
              res.writeHead(200, {'Content-Type': 'text/html'});
              res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                  <title>AI Nest Backend - API Documentation</title>
                  <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    h1 { color: #2c3e50; }
                    .endpoint { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
                    .method { font-weight: bold; color: #27ae60; }
                    .status { color: #e74c3c; font-weight: bold; }
                  </style>
                </head>
                <body>
                  <div class="container">
                    <h1>🚀 AI Nest Backend - API Documentation</h1>
                    <p><strong>Status:</strong> <span class="status">Running Successfully!</span></p>
                    <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'production'}</p>
                    <p><strong>Database:</strong> ${process.env.DB_HOST || 'postgres-service'}:${process.env.DB_PORT || '5432'}</p>
                    
                    <h2>📋 Available Endpoints</h2>
                    
                    <div class="endpoint">
                      <div class="method">GET /oauth2/status</div>
                      <div>Health check endpoint - Returns application status</div>
                    </div>
                    
                    <div class="endpoint">
                      <div class="method">GET /api-docs</div>
                      <div>This API documentation page</div>
                    </div>
                    
                    <div class="endpoint">
                      <div class="method">GET /</div>
                      <div>Root endpoint - Redirects to API documentation</div>
                    </div>
                    
                    <h2>🎯 Next Steps</h2>
                    <p>This is a simplified version of the AI Nest Backend running successfully in Kubernetes!</p>
                    <p>The full NestJS application with authentication, OAuth2, and database features can be deployed once the Docker image is built and available.</p>
                    
                    <h2>🔗 Quick Links</h2>
                    <ul>
                      <li><a href="/oauth2/status">Health Check</a></li>
                      <li><a href="/api-docs">API Documentation</a></li>
                    </ul>
                  </div>
                </body>
                </html>
              `);
            } else {
              res.writeHead(404);
              res.end(JSON.stringify({
                error: 'Not Found',
                message: 'Endpoint not found',
                availableEndpoints: ['/oauth2/status', '/api-docs', '/']
              }));
            }
          });
          
          const port = process.env.PORT || 8080;
          server.listen(port, '0.0.0.0', () => {
            console.log(\`🚀 AI Nest Backend (Simple) running on port \${port}\`);
            console.log(\`📚 API Documentation: http://localhost:\${port}/api-docs\`);
            console.log(\`🏥 Health Check: http://localhost:\${port}/oauth2/status\`);
          });
          EOF
          
          node server.js
        ports:
        - containerPort: 8080
          name: http
        env:
        # Configuration from ConfigMap
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: PORT
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: ai-nest-backend-config
              key: DB_NAME
        livenessProbe:
          httpGet:
            path: /oauth2/status
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /oauth2/status
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      restartPolicy: Always

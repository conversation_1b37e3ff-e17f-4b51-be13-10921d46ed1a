# AI Nest Backend - Complete GitOps Deployment Guide

## 🎯 Overview

This guide provides comprehensive instructions for deploying the AI Nest Backend using GitOps with Argo CD on Minikube. The deployment includes a NestJS authentication API with PostgreSQL database, complete with automated sync policies and monitoring.

## 📋 Prerequisites

### Required Software
- **Minikube**: Local Kubernetes cluster
- **kubectl**: Kubernetes command-line tool
- **Docker**: Container runtime
- **Git**: Version control (for repository access)

### Verify Prerequisites
```powershell
# Check Minikube
minikube status

# Check kubectl
kubectl version --client

# Check Docker
docker --version

# Check Git
git --version
```

## 🏗️ Architecture

### Application Components
- **NestJS API**: Authentication backend with JWT and OAuth2
- **PostgreSQL**: Database for user data
- **Argo CD**: GitOps deployment automation
- **Kubernetes**: Container orchestration

### Network Architecture
- **NodePort Service**: External access on port 30080
- **ClusterIP Services**: Internal communication
- **Ingress**: Optional external routing
- **Health Checks**: Automated monitoring

## 🚀 Deployment Process

### Phase 1: Repository Setup

1. **Ensure Repository Access**
   ```bash
   # If repository is private, configure Git credentials
   git config --global credential.helper store
   
   # Test repository access
   git clone https://github.com/ChidhagniConsulting/ai-nest-backend.git
   ```

2. **Verify Kubernetes Manifests**
   ```powershell
   # Navigate to project directory
   cd ai-nest-backend
   
   # Validate manifests
   kubectl apply --dry-run=client -f k8s/namespace.yaml
   kubectl apply --dry-run=client -f k8s/deployment.yaml
   ```

### Phase 2: Argo CD Configuration

1. **Access Argo CD UI**
   ```powershell
   # Start port-forward (keep this terminal open)
   kubectl port-forward svc/argocd-server -n argocd 8080:80
   
   # Get admin password
   kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | ForEach-Object { [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($_)) }
   ```

2. **Login Credentials**
   - **URL**: http://localhost:8080
   - **Username**: admin
   - **Password**: `Goosu3hzo7kEqRgd` (from command above)

3. **Create Argo CD Application**
   ```powershell
   # Apply project and application
   kubectl apply -f argocd/project.yaml
   kubectl apply -f argocd/application.yaml
   ```

### Phase 3: Repository Configuration in Argo CD

1. **Add Repository in Argo CD UI**
   - Navigate to Settings → Repositories
   - Click "Connect Repo using HTTPS"
   - **Repository URL**: `https://github.com/ChidhagniConsulting/ai-nest-backend.git`
   - **Username**: Your GitHub username (if private repo)
   - **Password**: Your GitHub personal access token (if private repo)

2. **Configure Application**
   - Navigate to Applications
   - Find "ai-nest-backend" application
   - Click "Sync" to deploy

### Phase 4: Deployment Verification

1. **Monitor Deployment**
   ```powershell
   # Check application status
   kubectl get application ai-nest-backend -n argocd
   
   # Monitor pods
   kubectl get pods -n ai-nest-backend -w
   
   # Check services
   kubectl get svc -n ai-nest-backend
   ```

2. **Verify Application Access**
   ```powershell
   # Get service URL
   minikube service ai-nest-backend-service -n ai-nest-backend --url
   
   # Test health endpoint
   curl http://<minikube-ip>:30080/oauth2/status
   ```

## 🔧 Configuration Details

### Environment Variables

#### ConfigMap (Non-sensitive)
- `NODE_ENV`: production
- `PORT`: 8080
- `DB_HOST`: postgres-service
- `DB_PORT`: 5432
- `DB_NAME`: userauth
- CORS, SMTP, OAuth2 settings

#### Secrets (Base64 encoded)
- `DB_USER`: postgres
- `DB_PASSWORD`: password
- `JWT_SECRET`: supersecretkey
- `SMTP_USER`: Email service username
- `SMTP_PASS`: Email service password
- `GOOGLE_CLIENT_ID`: OAuth client ID
- `GOOGLE_CLIENT_SECRET`: OAuth client secret

### Resource Allocation

#### Application Pod
- **Requests**: 512Mi memory, 250m CPU
- **Limits**: 1Gi memory, 500m CPU
- **Replicas**: 2

#### Database Pod
- **Requests**: 256Mi memory, 250m CPU
- **Limits**: 512Mi memory, 500m CPU
- **Storage**: 5Gi persistent volume

## 🔍 Health Checks

### Application Health
- **Liveness Probe**: `/api/v1/oauth2/status` (60s initial delay)
- **Readiness Probe**: `/api/v1/oauth2/status` (30s initial delay)

### Database Health
- **Liveness Probe**: `pg_isready -U postgres` (30s initial delay)
- **Readiness Probe**: `pg_isready -U postgres` (5s initial delay)

## 🌐 External Access

### NodePort Service
- **External Port**: 30080
- **Internal Port**: 8080
- **Access URL**: `http://<minikube-ip>:30080`

### Available Endpoints
- **Health Check**: `/api/v1/oauth2/status`
- **API Documentation**: `/api-docs`
- **OAuth2 Login**: `/api/v1/auth/google`
- **API Base**: `/api/v1`

## 🔒 Security Configuration

### Production Security Checklist
- [ ] Change default database passwords
- [ ] Generate new JWT secret
- [ ] Update OAuth2 credentials
- [ ] Configure proper SMTP credentials
- [ ] Set up proper RBAC
- [ ] Enable network policies
- [ ] Use sealed secrets or external secret management

### Secret Management
```powershell
# Generate new JWT secret
$jwtSecret = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes("your-new-super-secret-key"))

# Update secret
kubectl patch secret ai-nest-backend-secrets -n ai-nest-backend -p='{"data":{"JWT_SECRET":"'$jwtSecret'"}}'
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Repository Access Error
**Error**: "Repository not found" or "authentication required"
**Solution**:
- Verify repository URL is correct
- Add repository credentials in Argo CD UI
- Check if repository is public or private

#### 2. Pod Startup Issues
**Error**: Pods stuck in "Pending" or "CrashLoopBackOff"
**Solution**:
```powershell
# Check pod events
kubectl describe pod <pod-name> -n ai-nest-backend

# Check logs
kubectl logs <pod-name> -n ai-nest-backend

# Check resource availability
kubectl top nodes
kubectl top pods -n ai-nest-backend
```

#### 3. Database Connection Issues
**Error**: Application can't connect to database
**Solution**:
```powershell
# Check database pod
kubectl get pods -n ai-nest-backend -l app=postgres

# Test database connectivity
kubectl exec -it deployment/postgres -n ai-nest-backend -- pg_isready -U postgres

# Check service endpoints
kubectl get endpoints -n ai-nest-backend
```

#### 4. Service Access Issues
**Error**: Can't access application externally
**Solution**:
```powershell
# Check service configuration
kubectl get svc ai-nest-backend-service -n ai-nest-backend

# Get Minikube IP
minikube ip

# Check if port is accessible
telnet <minikube-ip> 30080
```

### Diagnostic Commands
```powershell
# Complete system status
kubectl get all -n ai-nest-backend

# Check Argo CD application
kubectl get application ai-nest-backend -n argocd -o yaml

# View application logs
kubectl logs -f deployment/ai-nest-backend -n ai-nest-backend

# Check resource usage
kubectl top pods -n ai-nest-backend
```

## 📊 Monitoring and Maintenance

### Regular Health Checks
```powershell
# Application health
curl http://<minikube-ip>:30080/api/v1/oauth2/status

# Database health
kubectl exec deployment/postgres -n ai-nest-backend -- pg_isready -U postgres

# Argo CD sync status
kubectl get application ai-nest-backend -n argocd
```

### Scaling Operations
```powershell
# Scale application
kubectl scale deployment ai-nest-backend -n ai-nest-backend --replicas=3

# Update resources
kubectl patch deployment ai-nest-backend -n ai-nest-backend -p='{"spec":{"template":{"spec":{"containers":[{"name":"ai-nest-backend","resources":{"limits":{"memory":"2Gi"}}}]}}}}'
```

## 🔄 GitOps Workflow

### Automated Sync Policy
- **Self-Heal**: Enabled (reverts manual changes)
- **Auto-Prune**: Enabled (removes orphaned resources)
- **Sync Options**: CreateNamespace, PruneLast

### Manual Operations
```powershell
# Force sync
kubectl patch application ai-nest-backend -n argocd --type merge -p='{"operation":{"sync":{"syncStrategy":{"hook":{"force":true}}}}}'

# Refresh application
kubectl patch application ai-nest-backend -n argocd --type merge -p='{"operation":{"initiatedBy":{"automated":false}}}'
```

## 📚 Additional Resources

- **Kubernetes Documentation**: https://kubernetes.io/docs/
- **Argo CD Documentation**: https://argo-cd.readthedocs.io/
- **NestJS Documentation**: https://nestjs.com/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/

## 🎉 Success Criteria

✅ **Deployment Complete When**:
- All pods are running and healthy
- Application responds to health checks
- Database is accessible and initialized
- External access works via NodePort
- Argo CD shows "Synced" and "Healthy" status
- API documentation is accessible
- OAuth2 endpoints are functional

import type { Knex } from 'knex';
import * as dotenv from 'dotenv';
import * as path from 'path';

const envFile = process.env.NODE_ENV === 'development'
  ? path.resolve(__dirname, '../../.env.development')
  : path.resolve(__dirname, '../../.env');

dotenv.config({ path: envFile });

const config: { [key: string]: Knex.Config } = {
  
  development: {
    client: 'pg',
    connection: {
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT ?? 5432),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    },
    migrations: {
      directory: __dirname + '/migrations',
      extension: 'ts',
    },
    pool: { min: 2, max: 10 },
    debug: true,
  },
};

export default config; 
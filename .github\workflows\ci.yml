name: CI Workflow
on:
  push:
    branches: [ "main","auth*" ]
jobs:
  build:
    runs-on: [self-hosted, minikube]
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build project
        run: npm run build
      - name: Set CI environment variable
        run: echo "CI_ENVIRONMENT=true" >> $GITHUB_ENV

      - name: Run tests (uses testcontainers, no external DB needed)
        env:
          CI_ENVIRONMENT: true
          NODE_ENV: test
        run: npm run test:ci
      - name: Log in to Docker Hub
        run: echo ${{ secrets.DOCKER_TOKEN }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
      - name: Build Docker image
        run: docker build -t ${{ secrets.DOCKER_USERNAME }}/ai-nest-backend:latest .
      - name: Push Docker image
        run: docker push ${{ secrets.DOCKER_USERNAME }}/ai-nest-backend:latest
# Stage 1: Build
FROM node:20-alpine AS builder

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

# Stage 2: Production image
FROM node:20-alpine

WORKDIR /app

COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY package*.json ./

# The .env file should be provided at runtime via a volume mount or Docker Compose

# Set NODE_ENV (can be overridden at runtime)
ENV NODE_ENV=production

CMD ["node", "dist/src/main.js"] 
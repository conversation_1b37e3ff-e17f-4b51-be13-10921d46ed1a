import { AuthService } from '../../src/auth/auth.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { EmailService } from '../../src/email/email.service';
import { setupTestDb, teardownTestDb, cleanDb, TestDbContext } from './test-utils';
import { Logger } from '@nestjs/common';

jest.setTimeout(120000);

describe('AuthService (Integration)', () => {
  let dbCtx: TestDbContext;
  let authService: AuthService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let emailService: EmailService;


  beforeAll(async () => {
    dbCtx = await setupTestDb();
  });

  afterAll(async () => {
    await teardownTestDb(dbCtx);
  });

  beforeEach(async () => {
    await cleanDb(dbCtx.knex);
    jwtService = { sign: jest.fn(() => 'jwt-token') } as any;
    configService = { get: jest.fn() } as any;
    emailService = { sendVerificationLink: jest.fn(), sendPasswordResetLink: jest.fn() } as any;
    authService = new AuthService(jwtService, configService, emailService, dbCtx.knex);
  });

  it('registers a new user and sends verification email', async () => {
    const dto = { email: '<EMAIL>', name: 'Reg Test', mobileNumber: '1234567890' };
    const result = await authService.register(dto as any);
    expect(result.message).toMatch(/Verification link sent/);
    expect(emailService.sendVerificationLink).toHaveBeenCalledWith(
      dto.email,
      expect.any(String)
    );
    // Check DB for verification record
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    expect(record).toBeDefined();
    expect(record.verified).toBe(false);
  });

  it('verifies email with valid token', async () => {
    // Register first
    const dto = { email: '<EMAIL>', name: 'Verify Me', mobileNumber: '1112223333' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    const result = await authService.verifyEmail(record.verification_token);
    expect(result.message).toMatch(/Email verified successfully/);
    const updated = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    expect(updated.verified).toBe(true);
  });

  it('fails to verify with invalid token', async () => {
    await expect(authService.verifyEmail('badtoken')).rejects.toThrow(/Invalid or expired token/);
  });

  it('creates a user after verification', async () => {
    // Register and verify
    const dto = { email: '<EMAIL>', name: 'New User', mobileNumber: '9998887777' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    // Create user
    const createDto = { email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    const result = await authService.createUser(createDto as any);
    expect(result.userId).toBeDefined();
    // User should exist in DB
    const user = await dbCtx.knex('users').where({ email: dto.email }).first();
    expect(user).toBeDefined();
    expect(user.email_verified).toBe(true);
  });

  it('fails to create user if not verified', async () => {
    const createDto = { email: '<EMAIL>', password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    await expect(authService.createUser(createDto as any)).rejects.toThrow(/Email not verified/);
  });

  it('resends verification link for unverified email', async () => {
    const dto = { email: '<EMAIL>', name: 'Resend', mobileNumber: '5551112222' };
    await authService.register(dto as any);
    const resendDto = { email: dto.email };
    const result = await authService.resendVerification(resendDto as any);
    expect(result.message).toMatch(/Verification link resent/);
    expect(emailService.sendVerificationLink).toHaveBeenCalledWith(
      dto.email,
      expect.any(String)
    );
  });

  it('fails to resend verification if already verified', async () => {
    const dto = { email: '<EMAIL>', name: 'Already Verified', mobileNumber: '5553334444' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    await expect(authService.resendVerification({ email: dto.email } as any)).rejects.toThrow(/already verified/);
  });

  it('sends password reset link for existing user', async () => {
    // Register, verify, create user
    const dto = { email: '<EMAIL>', name: 'Forgot', mobileNumber: '5556667777' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    await authService.createUser({ email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any);
    const forgotDto = { email: dto.email, ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    const result = await authService.forgotPassword(forgotDto as any);
    expect(result.message).toMatch(/Password reset link sent/);
    expect(emailService.sendPasswordResetLink).toHaveBeenCalledWith(
      dto.email,
      expect.any(String)
    );
  });

  it('fails to send password reset for non-existent user', async () => {
    const forgotDto = { email: '<EMAIL>', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    await expect(authService.forgotPassword(forgotDto as any)).rejects.toThrow(/User not found/);
  });

  it('resets password with valid token', async () => {
    // Register, verify, create user, request reset
    const dto = { email: '<EMAIL>', name: 'Reset Me', mobileNumber: '5558889999' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    await authService.createUser({ email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any);
    await authService.forgotPassword({ email: dto.email, ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any);
    const resetRecord = await dbCtx.knex('user_password_reset').where({}).orderBy('created_at', 'desc').first();
    const resetDto = { resetToken: resetRecord.reset_token, newPassword: 'NewPassword!1', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    const result = await authService.resetPassword(resetDto as any);
    expect(result.message).toMatch(/Password reset successful/);
    // Check that token is marked used
    const used = await dbCtx.knex('user_password_reset').where({ reset_token: resetRecord.reset_token }).first();
    expect(used.used).toBe(true);
  });

  it('fails to reset password with invalid token', async () => {
    const resetDto = { resetToken: 'badtoken', newPassword: 'Whatever123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    await expect(authService.resetPassword(resetDto as any)).rejects.toThrow(/Invalid or expired reset token/);
  });

  it('logs in with valid credentials', async () => {
    // Register, verify, create user
    const dto = { email: '<EMAIL>', name: 'Login Me', mobileNumber: '5550001111' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    await authService.createUser({ email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any);
    const loginDto = { email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    const result = await authService.login(loginDto as any);
    expect(result.userId).toBeDefined();
    expect(result.sessionToken).toBeDefined();
    expect(result.refreshToken).toBeDefined();
  });

  it('fails login with wrong password', async () => {
    // Register, verify, create user
    const dto = { email: '<EMAIL>', name: 'Wrong Pass', mobileNumber: '5552223333' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    await authService.createUser({ email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any);
    const loginDto = { email: dto.email, password: 'WrongPassword!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    await expect(authService.login(loginDto as any)).rejects.toThrow(/Invalid credentials/);
  });

  it('logs out a session', async () => {
    // Register, verify, create user, login
    const dto = { email: '<EMAIL>', name: 'Logout Me', mobileNumber: '5554445555' };
    await authService.register(dto as any);
    const record = await dbCtx.knex('user_verification').where({ contact_value: dto.email }).first();
    await authService.verifyEmail(record.verification_token);
    await authService.createUser({ email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any);
    const loginDto = { email: dto.email, password: 'Password123!', ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    const loginResult = await authService.login(loginDto as any);
    const logoutDto = { sessionToken: loginResult.sessionToken, ipAddress: '127.0.0.1', deviceDetails: 'test-device' };
    const result = await authService.logout(logoutDto as any, { id: loginResult.userId });
    expect(result.message).toMatch(/Logout successful/);
    // Check session is inactive
    const session = await dbCtx.knex('user_sessions').where({ session_token: loginResult.sessionToken }).first();
    expect(session.is_active).toBe(false);
  });

  it('fails to logout with invalid session token', async () => {
    await expect(authService.logout({ sessionToken: 'badtoken', ipAddress: '127.0.0.1', deviceDetails: 'test-device' } as any, { id: 'fakeid' })).rejects.toThrow(/Session not found/);
  });
}); 
name: CI Workflow
on:
  push:
    branches: [ "main","auth*" ]
jobs:
  build:
    runs-on: [self-hosted, minikube]
    strategy:
      matrix:
        node-version: [20.x, 22.x]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build project
        run: npm run build
      - name: Start PostgreSQL with Docker Compose
        run: docker-compose up -d postgres
      
      - name: Wait for PostgreSQL to be ready
        run: |
          for i in {1..15}; do
            if docker exec userauth-postgres pg_isready -U postgres; then
              echo "Postgres is ready!"
              break
            fi
            echo "Waiting for Postgres..."
            sleep 5
          done

      - name: Check Postgres connection
        run: |
            docker ps
            docker exec userauth-postgres pg_isready -U postgres -h localhost -p 5432
      
      - name: Run DB migrations
        env:
          DB_HOST: localhost
          DB_PORT: 5433
          DB_USER: postgres
          DB_PASSWORD: password
          DB_NAME: userauth
        run: npm run migration:latest
      - name: Seed DB
        run: npm run db:seed
      - name: Run tests
        run: npm test
      - name: Log in to Docker Hub
        run: echo ${{ secrets.DOCKER_TOKEN }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
      - name: Build Docker image
        run: docker build -t ${{ secrets.DOCKER_USERNAME }}/ai-nest-backend:latest .
      - name: Push Docker image
        run: docker push ${{ secrets.DOCKER_USERNAME }}/ai-nest-backend:latest
# AI Nest Backend - Repository Access Fix Script
# This script helps resolve repository access issues in Argo CD

Write-Host "🔧 AI Nest Backend - Repository Access Fix" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Function to check repository accessibility
function Test-RepositoryAccess {
    param($repoUrl)
    
    Write-Host "`n🔍 Testing repository access..." -ForegroundColor Yellow
    try {
        $tempDir = Join-Path $env:TEMP "argocd-repo-test"
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force
        }
        
        git clone $repoUrl $tempDir --depth 1 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Repository is accessible" -ForegroundColor Green
            Remove-Item $tempDir -Recurse -Force
            return $true
        } else {
            Write-Host "❌ Repository access failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error testing repository access: $_" -ForegroundColor Red
        return $false
    }
}

# Function to update Argo CD application with correct repository
function Update-ArgoApplication {
    param($repoUrl)
    
    Write-Host "`n📝 Updating Argo CD application..." -ForegroundColor Yellow
    try {
        # Update the application with the correct repository URL
        kubectl patch application ai-nest-backend -n argocd --type merge -p="{`"spec`":{`"source`":{`"repoURL`":`"$repoUrl`"}}}"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Application updated successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to update application" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error updating application: $_" -ForegroundColor Red
        return $false
    }
}

# Function to create repository secret for private repos
function Create-RepositorySecret {
    param($repoUrl, $username, $password)
    
    Write-Host "`n🔐 Creating repository secret..." -ForegroundColor Yellow
    
    $secretYaml = @"
apiVersion: v1
kind: Secret
metadata:
  name: ai-nest-backend-repo
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
type: Opaque
stringData:
  type: git
  url: $repoUrl
  username: $username
  password: $password
"@
    
    try {
        $secretYaml | kubectl apply -f -
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Repository secret created successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to create repository secret" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating repository secret: $_" -ForegroundColor Red
        return $false
    }
}

# Main script execution
Write-Host "`n🚀 Starting repository access fix..." -ForegroundColor Green

# Check current application status
Write-Host "`n📊 Checking current application status..." -ForegroundColor Yellow
$currentStatus = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.sync.status}' 2>$null
$currentRepo = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.spec.source.repoURL}' 2>$null

Write-Host "Current repository: $currentRepo" -ForegroundColor Cyan
Write-Host "Current sync status: $currentStatus" -ForegroundColor Cyan

# Test repository access
$repoAccessible = Test-RepositoryAccess -repoUrl $currentRepo

if (-not $repoAccessible) {
    Write-Host "`n🔄 Repository access failed. Trying alternative solutions..." -ForegroundColor Yellow
    
    # Option 1: Try with .git suffix
    $repoWithGit = $currentRepo
    if (-not $repoWithGit.EndsWith('.git')) {
        $repoWithGit += '.git'
        Write-Host "`n🔄 Trying repository URL with .git suffix: $repoWithGit" -ForegroundColor Yellow
        
        if (Test-RepositoryAccess -repoUrl $repoWithGit) {
            Update-ArgoApplication -repoUrl $repoWithGit
        }
    }
    
    # Option 2: Check if repository is private
    Write-Host "`n❓ Is this a private repository? (y/n): " -ForegroundColor Yellow -NoNewline
    $isPrivate = Read-Host
    
    if ($isPrivate -eq 'y' -or $isPrivate -eq 'Y') {
        Write-Host "`n🔐 Setting up private repository access..." -ForegroundColor Yellow
        
        $username = Read-Host "Enter GitHub username"
        $password = Read-Host "Enter GitHub personal access token (not password)" -AsSecureString
        $passwordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))
        
        Create-RepositorySecret -repoUrl $currentRepo -username $username -password $passwordPlain
    }
    
    # Option 3: Use local repository for testing
    Write-Host "`n💡 Alternative: Deploy from local manifests for testing" -ForegroundColor Yellow
    Write-Host "Would you like to deploy directly from local k8s manifests? (y/n): " -NoNewline
    $deployLocal = Read-Host
    
    if ($deployLocal -eq 'y' -or $deployLocal -eq 'Y') {
        Write-Host "`n📦 Deploying from local manifests..." -ForegroundColor Yellow
        
        # Deploy namespace first
        kubectl apply -f k8s/namespace.yaml
        
        # Deploy configuration
        kubectl apply -f k8s/configmap.yaml
        kubectl apply -f k8s/secret.yaml
        
        # Deploy database
        kubectl apply -f k8s/postgres-pvc.yaml
        kubectl apply -f k8s/postgres-deployment.yaml
        kubectl apply -f k8s/postgres-service.yaml
        
        # Wait for database
        Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
        kubectl wait --for=condition=ready pod -l app=postgres -n ai-nest-backend --timeout=300s
        
        # Deploy application
        kubectl apply -f k8s/deployment.yaml
        kubectl apply -f k8s/service.yaml
        
        # Run migration job
        kubectl apply -f k8s/migration-job.yaml
        
        Write-Host "✅ Local deployment completed!" -ForegroundColor Green
        Write-Host "🌐 Access the application at: http://$(minikube ip):30080" -ForegroundColor Cyan
    }
}

# Final status check
Write-Host "`n📊 Final status check..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

$finalStatus = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.sync.status}' 2>$null
$healthStatus = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.health.status}' 2>$null

Write-Host "Final sync status: $finalStatus" -ForegroundColor Cyan
Write-Host "Health status: $healthStatus" -ForegroundColor Cyan

if ($finalStatus -eq "Synced" -and $healthStatus -eq "Healthy") {
    Write-Host "`n🎉 Repository access fixed! Application is now synced and healthy." -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Repository access may still need attention. Check Argo CD UI for details." -ForegroundColor Yellow
    Write-Host "Access Argo CD at: http://localhost:8080" -ForegroundColor Cyan
    Write-Host "Username: admin" -ForegroundColor Cyan
    Write-Host "Password: Goosu3hzo7kEqRgd" -ForegroundColor Cyan
}

Write-Host "`n🔧 Repository access fix completed!" -ForegroundColor Green

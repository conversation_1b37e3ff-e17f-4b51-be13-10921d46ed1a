// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { EmailService } from '../../src/email/email.service';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';

describe('EmailService (Integration)', () => {
  let emailService: EmailService;
  let mailerService: MailerService;
  let configService: ConfigService;

  beforeEach(() => {
    // Set test environment
    process.env.NODE_ENV = 'test';
    
    mailerService = {
      sendMail: jest.fn().mockResolvedValue(true),
    } as any;
    configService = {
      get: jest.fn((key: string) => {
        if (key === 'email.host') return undefined; // Simulate no SMTP config (logs only)
        if (key === 'APP_URL') return 'http://localhost:3000';
        return undefined;
      }),
    } as any;
    emailService = new EmailService(mailerService, configService);
  });

  it('should log verification email when no SMTP is configured', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    await emailService.sendVerificationLink('<EMAIL>', 'token123');
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
    consoleSpy.mockRestore();
  });

  it('should log password reset email when no SMTP is configured', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    await emailService.sendPasswordResetLink('<EMAIL>', 'token123');
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
    consoleSpy.mockRestore();
  });

  describe('with configured SMTP', () => {
    beforeEach(() => {
      // Set environment to 'smtp' for configured SMTP tests
      process.env.NODE_ENV = 'smtp';
      
      configService = {
        get: jest.fn((key: string) => {
          switch (key) {
            case 'email.host': return 'smtp.gmail.com';
            case 'email.port': return '587';
            case 'email.user': return '<EMAIL>';
            case 'email.pass': return 'testpass';
            case 'email.from': return '<EMAIL>';
            case 'APP_URL': return 'http://localhost:3000';
            default: return undefined;
          }
        }),
      } as any;
      emailService = new EmailService(mailerService, configService);
    });

    it('should throw for daily limit exceeded', async () => {
      // Manually set usage to limit
      (emailService as any).emailProvider.currentUsage = 500;
      await expect(emailService.sendVerificationLink('<EMAIL>', 'token123')).rejects.toThrow('Daily email sending limit exceeded');
    });
  });
}); 
# ⚡ Quick Deployment Guide

## 🚀 One-Command Deployment

### Prerequisites Check
```bash
minikube status && kubectl cluster-info
```

### Install ArgoCD
```bash
kubectl create namespace argocd
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
kubectl wait --for=condition=available --timeout=300s deployment/argocd-server -n argocd
```

### Deploy Application
```bash
git clone https://github.com/ChidhagniConsulting/ai-nest-backend.git
cd ai-nest-backend
git checkout auth-docker-ci
kubectl apply -f argocd/application.yaml
```

### Monitor Deployment
```bash
kubectl get application ai-nest-backend -n argocd -w
kubectl get pods -n ai-nest-backend -w
```

### Access Application
```bash
minikube service ai-nest-backend-service -n ai-nest-backend --url
```

## 🔧 Essential Commands

### Check Status
```bash
# ArgoCD Application
kubectl get application ai-nest-backend -n argocd

# Pods
kubectl get pods -n ai-nest-backend

# Services
kubectl get svc -n ai-nest-backend

# All resources
kubectl get all -n ai-nest-backend
```

### Troubleshooting
```bash
# Application logs
kubectl logs -n ai-nest-backend deployment/ai-nest-backend

# Pod details
kubectl describe pod <pod-name> -n ai-nest-backend

# Force ArgoCD refresh
kubectl annotate application ai-nest-backend -n argocd argocd.argoproj.io/refresh=hard --overwrite

# Restart deployment
kubectl rollout restart deployment/ai-nest-backend -n ai-nest-backend
```

### Health Checks
```bash
# Test health endpoint
kubectl exec -n ai-nest-backend deployment/ai-nest-backend -- wget -qO- http://localhost:8080/api/v1/oauth2/status

# Database health
kubectl exec -n ai-nest-backend deployment/postgres -- pg_isready -U postgres
```

### ArgoCD Access
```bash
# Get admin password
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

# Port forward
kubectl port-forward svc/argocd-server -n argocd 8080:80

# Access: http://localhost:8080 (admin / password-from-above)
```

## 🎯 Expected Results

### Successful Deployment
```
NAME              SYNC STATUS   HEALTH STATUS
ai-nest-backend   Synced        Healthy
```

### Healthy Pods
```
NAME                               READY   STATUS      RESTARTS   AGE
ai-nest-backend-xxx                1/1     Running     0          5m
ai-nest-backend-migration-xxx      0/1     Completed   0          5m
postgres-xxx                       1/1     Running     0          5m
```

### Working Endpoints
- Health: `http://<minikube-ip>:30080/api/v1/oauth2/status`
- API Docs: `http://<minikube-ip>:30080/api-docs`
- OAuth: `http://<minikube-ip>:30080/api/v1/auth/google`

## 🆘 Quick Fixes

### Sync Failed
```bash
kubectl delete application ai-nest-backend -n argocd
kubectl apply -f argocd/application.yaml
```

### Pod Not Ready
```bash
kubectl delete pod <pod-name> -n ai-nest-backend
# Wait for automatic recreation
```

### Health Check Failed
```bash
# Check if endpoint is correct
kubectl get deployment ai-nest-backend -n ai-nest-backend -o jsonpath='{.spec.template.spec.containers[0].livenessProbe.httpGet.path}'
# Should return: /api/v1/oauth2/status
```

## 🧹 Cleanup
```bash
kubectl delete application ai-nest-backend -n argocd
kubectl delete namespace ai-nest-backend
```

## 📞 Support

If deployment fails:
1. Check [DEPLOYMENT_STEPS.md](./DEPLOYMENT_STEPS.md) for detailed instructions
2. Review [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for troubleshooting
3. Check pod logs: `kubectl logs -n ai-nest-backend deployment/ai-nest-backend`
4. Verify ArgoCD status: `kubectl describe application ai-nest-backend -n argocd`

**Total deployment time: ~5-10 minutes** ⏱️

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-nest-backend-ingress
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/cors-allow-origin: "http://localhost:3000,http://localhost:3001"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: ai-nest-backend.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-nest-backend-service
            port:
              number: 8080

import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import {
  RegisterDto,
  CreateUserDto,
  ResendVerificationDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  LoginDto,
  LogoutDto,
} from "./dto";
import * as bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";
import { Knex } from "knex";
import { EmailService } from "../email/email.service";
import { Logger } from '@nestjs/common';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    @Inject("KNEX_CONNECTION") private readonly knex: Knex,
  ) {}

  // Utility: Generate a secure token
  private generateToken(): string {
    return uuidv4() + uuidv4();
  }

  // Utility: Get expiration date (default: 24 hours)
  private getExpiration(hours = 24): Date {
    return new Date(Date.now() + hours * 60 * 60 * 1000);
  }

  // Utility: Get refresh expiration (default: 30 days)
  private getRefreshExpiration(days = 30): Date {
    return new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  }

  // Utility: Create a user session
  private async createSession(
    userId: string,
    ipAddress: string,
    deviceDetails: string,
  ) {
    const sessionToken = this.generateToken();
    const refreshToken = this.generateToken();
    const now = new Date();
    const expiresAt = this.getExpiration(24);
    const refreshExpiresAt = this.getRefreshExpiration(30);
    await this.knex("user_sessions").insert({
      id: uuidv4(),
      user_id: userId,
      session_token: sessionToken,
      refresh_token: refreshToken,
      expires_at: expiresAt,
      refresh_expires_at: refreshExpiresAt,
      is_active: true,
      created_at: now,
      last_accessed_at: now,
      ip_address: ipAddress,
      device_details: deviceDetails,
    });
    return { sessionToken, refreshToken, expiresAt, refreshExpiresAt };
  }

  // Google OAuth2 authentication
  async googleAuth(googleUser: any, ipAddress: string, deviceDetails: string) {
    this.logger.log('Google OAuth2 authentication attempt', 'AuthService');
    const { email, firstName, lastName, picture, googleId } = googleUser;
    
    // Validate required fields
    if (!email || !googleId) {
      this.logger.warn('Invalid Google user data: missing email or Google ID', 'AuthService');
      throw new BadRequestException('Invalid Google user data: missing email or Google ID');
    }

    // Check if user exists by Google ID first
    let user = await this.knex("users").where({ google_id: googleId }).first();
    let isNewUser = false;
    
    if (!user) {
      // Check if user exists by email
      user = await this.knex("users").where({ email }).first();
      
      if (user) {
        // Update existing user with Google information
        await this.knex("users")
          .where({ id: user.id })
          .update({
            google_id: googleId,
            first_name: firstName ?? user.first_name,
            last_name: lastName ?? user.last_name,
            profile_picture: picture ?? user.profile_picture,
            social_login_provider: 'google',
            social_login_provider_id: googleId,
            social_login_provider_image_url: picture ?? user.social_login_provider_image_url,
            email_verified: true,
            updated_on: new Date(),
          });
      } else {
        // Create new user
        const userId = uuidv4();
        const userName = `${firstName ?? ''} ${lastName ?? ''}`.trim() || email.split('@')[0];
        await this.knex("users").insert({
          id: userId,
          email,
          password: null, // No password for OAuth users
          name: userName,
          first_name: firstName ?? '',
          last_name: lastName ?? '',
          profile_picture: picture ?? '',
          mobile_number: '', // Can be updated later
          is_active: true,
          email_verified: true,
          account_locked: false,
          failed_login_attempts: 0,
          social_login_provider: 'google',
          social_login_provider_id: googleId,
          social_login_provider_image_url: picture ?? '',
          google_id: googleId,
          created_on: new Date(),
          updated_on: new Date(),
        });
        user = await this.knex("users").where({ id: userId }).first();
        isNewUser = true;
      }
    }

    // If new user, insert into user_verification if not exists
    if (isNewUser) {
      const existingVerification = await this.knex("user_verification").where({ contact_value: email }).first();
      if (!existingVerification) {
        await this.knex("user_verification").insert({
          id: uuidv4(),
          contact_value: email,
          verification_token: this.generateToken(),
          name: `${firstName ?? ''} ${lastName ?? ''}`.trim() ?? email.split('@')[0],
          mobile_number: '',
          expires_at: this.getExpiration(24),
          verified: true,
          created_at: new Date(),
          verified_at: new Date(),
          ip_address: ipAddress,
          device_details: deviceDetails,
        });
      }
    }

    // Update last login
    await this.knex("users")
      .where({ id: user.id })
      .update({ 
        last_login_at: new Date(),
        updated_on: new Date()
      });

    // Deactivate previous active sessions for this user and device
    await this.knex("user_sessions")
      .where({ user_id: user.id, is_active: true, device_details: deviceDetails })
      .update({ is_active: false });

    // Create session
    const session = await this.createSession(user.id, ipAddress, deviceDetails);
    
    // Generate JWT token
    const payload = { 
      sub: user.id, 
      email: user.email,
      googleId: user.google_id 
    };
    const accessToken = this.jwtService.sign(payload);

    return {
      accessToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name ?? firstName ?? '',
        lastName: user.last_name ?? lastName ?? '',
        picture: user.profile_picture ?? picture ?? '',
        isEmailVerified: user.email_verified,
      },
      sessionToken: session.sessionToken,
      refreshToken: session.refreshToken,
      expiresAt: session.expiresAt.toISOString(),
      refreshExpiresAt: session.refreshExpiresAt.toISOString(),
    };
  }

  async register(dto: RegisterDto) {
    const { email, name, mobileNumber } = dto;
    if (!email || !name || !mobileNumber) {
      throw new BadRequestException('Missing required fields');
    }
    if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)) {
      throw new BadRequestException('Invalid email address');
    }
    const existing = await this.knex("user_verification")
      .where({ contact_value: email })
      .first();
    if (existing?.verified) {
      throw new BadRequestException("Email already verified.");
    }
    // Only check for duplicate mobile if not already verified for this email
    const duplicateMobile = await this.knex("user_verification")
      .where({ mobile_number: mobileNumber })
      .first();
    if (duplicateMobile) {
      throw new BadRequestException('Mobile number already in use');
    }
    const verification_token = this.generateToken();
    const expires_at = this.getExpiration(0.5);
    if (existing) {
      await this.knex("user_verification")
        .where({ contact_value: email })
        .update({
          verification_token,
          expires_at,
          verified: false,
          created_at: new Date(),
          name,
          mobile_number: mobileNumber,
        });
    } else {
      await this.knex("user_verification").insert({
        id: uuidv4(),
        contact_value: email,
        verification_token,
        expires_at,
        verified: false,
        created_at: new Date(),
        name,
        mobile_number: mobileNumber,
      });
    }
    await this.emailService.sendVerificationLink(email, verification_token);
    return { message: "Verification link sent" };
  }

  async verifyEmail(token: string) {
    const record = await this.knex("user_verification")
      .where({ verification_token: token })
      .first();
    if (!record) {
      throw new BadRequestException("Invalid or expired token.");
    }
    if (record.verified) {
      throw new BadRequestException("Email already verified.");
    }
    if (new Date(record.expires_at ?? record.expiresAt) < new Date()) {
      throw new BadRequestException("Token expired.");
    }
    await this.knex("user_verification")
      .where({ verification_token: token })
      .update({ verified: true, verified_at: new Date() });
    return {
      message: "Email verified successfully",
      email: record.contact_value ?? record.contactValue,
    };
  }

  async createUser(dto: CreateUserDto) {
    const { email, password, ipAddress, deviceDetails } = dto;
    const verification = await this.knex("user_verification")
      .where({ contact_value: email, verified: true })
      .first();
    if (!verification) {
      throw new BadRequestException("Email not verified.");
    }
    const existingUser = await this.knex("users").where({ email }).first();
    if (existingUser) {
      throw new BadRequestException("User already exists.");
    }
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await this.knex("users")
      .insert({
        id: uuidv4(),
        email,
        password: hashedPassword,
        name: verification.name,
        mobile_number: verification.mobile_number,
        email_verified: true,
        created_on: new Date(),
        updated_on: new Date(),
      })
      .returning("*");
    // Create session
    const session = await this.createSession(
      user[0].id,
      ipAddress,
      deviceDetails,
    );
    return {
      userId: user[0].id,
      sessionToken: session.sessionToken,
      refreshToken: session.refreshToken,
    };
  }

  async resendVerification(dto: ResendVerificationDto) {
    const { email } = dto;
    // Check for a verified record for this email only
    const verifiedRecord = await this.knex("user_verification")
      .where({ contact_value: email, verified: true })
      .first();
    if (verifiedRecord) {
      throw new BadRequestException("Email already verified.");
    }
    // If not verified, check for an unverified record
    const record = await this.knex("user_verification")
      .where({ contact_value: email, verified: false })
      .first();
    if (!record) {
      throw new BadRequestException(
        "No unverified record found for this email."
      );
    }
    const verification_token = this.generateToken();
    const expires_at = this.getExpiration(0.5);
    await this.knex("user_verification")
      .where({ contact_value: email })
      .update({ verification_token, expires_at, created_at: new Date() });
    await this.emailService.sendVerificationLink(email, verification_token);
    return { message: "Verification link resent" };
  }

  async forgotPassword(dto: ForgotPasswordDto) {
    const { email, ipAddress, deviceDetails } = dto;
    const user = await this.knex("users").where({ email }).first();
    if (!user) {
      throw new BadRequestException("User not found.");
    }
    const resetToken = this.generateToken();
    const expiresAt = this.getExpiration(0.5);
    await this.knex("user_password_reset").insert({
      id: uuidv4(),
      user_id: user.id,
      reset_token: resetToken,
      expires_at: expiresAt,
      used: false,
      created_at: new Date(),
      ip_address: ipAddress,
      device_details: deviceDetails,
    });
    await this.emailService.sendPasswordResetLink(email, resetToken);
    return { message: "Password reset link sent" };
  }

  async resetPassword(dto: ResetPasswordDto) {
    const { resetToken, newPassword,  ipAddress, deviceDetails } = dto;
    // Find the record regardless of 'used' status
    const record = await this.knex("user_password_reset")
      .where({ reset_token: resetToken })
      .first();
    if (!record) {
      throw new BadRequestException("Invalid or expired reset token.");
    }
    if (record.used) {
      throw new BadRequestException("Reset token already used.");
    }
    if (new Date(record.expires_at ?? record.expiresAt) < new Date()) {
      throw new BadRequestException("Reset token expired.");
    }
    // Fetch the user and check old password
    const user = await this.knex("users").where({ id: record.user_id ?? record.userId }).first();
    if (!user) {
      throw new BadRequestException("User not found.");
    }
    // Fix: Allow OAuth users (no password) to set a new password
    if (!user.password) {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      await this.knex("users")
        .where({ id: record.user_id ?? record.userId })
        .update({ password: hashedPassword });
      await this.knex("user_password_reset")
        .where({ reset_token: resetToken })
        .update({ used: true, used_at: new Date(), ip_address: ipAddress, device_details: deviceDetails });
      return { message: "Password reset successful" };
    }
    const isSameAsOld = await bcrypt.compare(newPassword, user.password);
    if (isSameAsOld) {
      throw new BadRequestException("New password cannot be the same as the old password.");
    }
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await this.knex("users")
      .where({ id: record.user_id ?? record.userId })
      .update({ password: hashedPassword });
    await this.knex("user_password_reset")
      .where({ reset_token: resetToken })
      .update({ used: true, used_at: new Date(), ip_address: ipAddress, device_details: deviceDetails });
    return { message: "Password reset successful" };
  }

  async login(dto: LoginDto) {
    const {
      email,
      password,
      ipAddress,
      deviceDetails,
      overrideExistingLogins,
    } = dto;
    const user = await this.knex("users").where({ email }).first();
    if (!user) {
      throw new UnauthorizedException("Invalid credentials.");
    }
    const valid = await bcrypt.compare(password, user.password);
    if (!valid) {
      throw new UnauthorizedException("Invalid credentials.");
    }
    if (overrideExistingLogins) {
      await this.knex("user_sessions")
        .where({ user_id: user.id, is_active: true })
        .update({ is_active: false, logged_out_at: new Date() });
    }
    const session = await this.createSession(
      user.id,
      ipAddress,
      deviceDetails,
    );
    return {
      userId: user.id,
      sessionToken: session.sessionToken,
      refreshToken: session.refreshToken,
      expiresAt: session.expiresAt.toISOString(),
      refreshExpiresAt: session.refreshExpiresAt.toISOString(),
    };
  }

  async logout(dto: LogoutDto, user: any) {
    const { sessionToken, ipAddress, deviceDetails } = dto;
    const session = await this.knex("user_sessions")
      .where({ session_token: sessionToken, is_active: true })
      .first();
    if (!session) {
      throw new BadRequestException("Session not found or already logged out.");
    }
    await this.knex("user_sessions")
      .where({ session_token: sessionToken })
      .update({
        is_active: false,
        logged_out_at: new Date(),
        ip_address: ipAddress,
        device_details: deviceDetails,
      });
    return { message: "Logout successful" };
  }

  // Method to get email service for status checking
  getEmailService() {
    return this.emailService;
  }

  /* istanbul ignore next */
  // Fetch verification token by email
  async getVerificationTokenByEmail(email: string): Promise<string> {
    const record = await this.knex("user_verification").where({ contact_value: email }).first();
    /* istanbul ignore if */
    if (!record) {
      throw new BadRequestException('No verification token found for this email');
    }
    return record.verification_token;
  }
}

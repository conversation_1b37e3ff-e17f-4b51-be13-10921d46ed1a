import { IsEmail, IsString, Length } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({ 
    description: 'User email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  email!: string;

  @ApiProperty({ 
    description: 'User full name',
    example: '<PERSON>'
  })
  @IsString()
  @Length(1, 100)
  name!: string;

  @ApiProperty({ 
    description: 'User mobile number (10 digits)',
    example: '1234567890'
  })
  @IsString()
  @Length(10, 10)
  mobileNumber!: string;
} 
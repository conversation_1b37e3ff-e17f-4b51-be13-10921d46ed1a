# Testing Strategy for ai-nest-backend

## Overview
This project uses a layered testing approach:
- **Unit Tests**: Isolate logic, mock all dependencies, no real DB.
- **Integration Tests**: Use a real database (via Docker/Testcontainers), test DB interactions.
- **E2E/API Tests**: Simulate real HTTP requests, test full routing and response.

## Directory Structure
```
test/
  unit/         # Unit tests (mocks only)
  integration/  # Integration tests (real DB)
  api/ or e2e/  # E2E/API tests (Supertest)
```

## Running Tests
- **All tests:**
  ```bash
  npm test
  ```
- **Unit only:**
  ```bash
  npm run test:unit
  ```
- **Integration only:**
  ```bash
  npm run test:integration
  ```
- **E2E/API only:**
  ```bash
  npm run test:e2e
  ```
- **Coverage report:**
  ```bash
  npm run test:cov
  ```

## Coverage
- Jest enforces >80% coverage for statements, branches, functions, and lines.
- Coverage reports are output to the `coverage/` directory.

## Environment
- Use `.env.test` for test DB and secrets.
- Integration/E2E tests require a running test database (see `docker-compose.yml`).

## Best Practices
- **Unit:** Use `jest.fn()`, `jest.mock()`, or `ts-mockito`. No real DB.
- **Integration:** Use real DB, run migrations/seeds, clean DB between tests.
- **E2E:** Use `@nestjs/testing` and `supertest`. Bootstrap/teardown app per suite.
- **Arrange-Act-Assert:** Structure all tests for clarity.
- **Positive/Negative Cases:** Test both success and failure scenarios.

## CI
- (Optional) Configure CI to run all test layers on PRs/merges.

## Test Types & How to Run

### Unit Tests
- Run: `npm run test`
- All dependencies (DB, email, external services) **must be mocked** using `jest.fn()` or similar. No real infrastructure calls are allowed.
- If you see a real DB/email call in a unit test, refactor it to use mocks.

### Integration Tests
- Run: `npm run test:integration`
- Uses [Testcontainers](https://www.testcontainers.org/) to spin up a real PostgreSQL DB in Docker for each test run.
- No shared or local dev DB is used.
- Test DB is started/stopped automatically in `test/integration/test-utils.ts`.
- **Seeding:**
  - Place seed scripts in the `seeds/` folder (see below for structure).
  - Integration tests should call `knex.seed.run()` to load seed data before tests.
  - Seeds should be idempotent and easy to maintain.

### E2E (API) Tests
- Run: `npm run test:e2e`
- Uses `supertest` to hit real HTTP endpoints.
- Uses the same Testcontainer DB setup as integration tests.
- Covers all major controllers and edge cases (success, validation, 404, auth errors).

## Seeding Data for Tests
- Create seed files in `seeds/` (e.g., `seeds/users.seed.ts`).
- Use `knex.seed.run()` in your test setup to load them.
- Example:
  ```ts
  await knex.seed.run({ directory: './seeds' });
  ```
- Seeds should be idempotent (safe to run multiple times).

## Mocking in Unit Tests
- All infrastructure dependencies must be mocked using `jest.fn()` or `ts-mockito`.
- No real DB, email, or external service calls are allowed in unit tests.
- If you find a real call, refactor to use a mock and add a warning comment.

## Coverage Reports
- Jest outputs HTML and LCOV reports to the `coverage/` folder.
- To view the HTML report:
  ```sh
  npx jest --coverage && npx serve coverage/lcov-report
  ```
- Coverage threshold is set to 80% for statements, branches, functions, and lines.

## Coverage Badge
- To generate a local badge:
  1. Run `npx jest --coverage`.
  2. Use [coverage-badges](https://www.npmjs.com/package/coverage-badges) or [coveralls](https://coveralls.io/) to generate/upload a badge.
- Add the badge to the top of your `README.md`.

## Folder Structure
```
test/
  unit/
  integration/
  e2e/
seeds/
```

## Testcontainer DB Setup
- Integration and E2E tests use Testcontainers to spin up a fresh PostgreSQL DB for each test run.
- See `test/integration/test-utils.ts` for setup details.
- No local or shared DB is used for tests.

---
For more, see `README.md` or ask the maintainers. 
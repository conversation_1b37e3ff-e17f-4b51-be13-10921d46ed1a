import { Controller, Post, Get, Body, Query, Req, UseGuards, Res, HttpStatus, NotImplementedException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { RegisterDto, CreateUserDto, ResendVerificationDto, ForgotPasswordDto, ResetPasswordDto, LoginDto, LogoutDto } from './dto';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import { Logger } from '@nestjs/common';

@ApiTags('Authentication')
@Controller()
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async register(@Body() dto: RegisterDto) {
    this.logger.log('Register endpoint called', 'AuthController');
    return this.authService.register(dto);
  }

  @Get('verify-email')
  @ApiOperation({ summary: 'Verify email with token' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async verifyEmail(@Query('token') token: string) { return this.authService.verifyEmail(token); }

  @Post('create-user')
  @ApiOperation({ summary: 'Create a new user (admin only)' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createUser(@Body() dto: CreateUserDto) { return this.authService.createUser(dto); }

  @Post('resend-verification-link')
  @ApiOperation({ summary: 'Resend verification email' })
  @ApiResponse({ status: 200, description: 'Verification email sent' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async resendVerification(@Body() dto: ResendVerificationDto) { return this.authService.resendVerification(dto); }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async forgotPassword(@Body() dto: ForgotPasswordDto) { return this.authService.forgotPassword(dto); }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async resetPassword(@Body() dto: ResetPasswordDto) { return this.authService.resetPassword(dto); }

  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async login(@Body() dto: LoginDto) {
    this.logger.log('Login endpoint called', 'AuthController');
    return this.authService.login(dto);
  }

  @Post('logout')
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async logout(@Body() dto: LogoutDto, @Req() req: Request) { return this.authService.logout(dto, req.user); }

  @Get('get-verification-token')
  @ApiOperation({ summary: 'Fetch verification token by email' })
  @ApiResponse({ status: 200, description: 'Verification token fetched' })
  @ApiResponse({ status: 400, description: 'No verification token found for this email' })
  /* istanbul ignore next */
  async getVerificationToken(@Query('email') email: string) {
    const token = await this.authService.getVerificationTokenByEmail(email);
    return { verification_token: token };
  }
}

@ApiTags('OAuth2')
@Controller()
export class OAuth2Controller {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  @Get('auth/google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Initiate Google OAuth2 login' })
  @ApiResponse({ status: 302, description: 'Redirect to Google OAuth' })
  // The 'google' AuthGuard handles the redirect. This method is not called.
  // It is present to associate the route with the guards and swagger documentation.
  googleAuth() {
    throw new NotImplementedException('This endpoint is handled by the Google OAuth guard.');
  }

  @Get('oauth2/status')
  @ApiOperation({ summary: 'Get OAuth2 configuration status' })
  @ApiResponse({ status: 200, description: 'OAuth2 status retrieved' })
  async getOAuth2Status() {
    const googleConfig = this.configService.get('googleOAuth');
    return {
      success: true,
      message: 'OAuth2 configuration status',
      timestamp: new Date().toISOString(),
      googleOAuth: {
        clientId: googleConfig.clientId ? 'Configured' : 'Not configured',
        clientSecret: googleConfig.clientSecret ? 'Configured' : 'Not configured',
        redirectUri: googleConfig.redirectUri,
        scope: googleConfig.scope,
        authorizedRedirectUris: googleConfig.authorizedRedirectUris,
      },
      endpoints: {
        authorize: '/auth/google',
        callback: '/oauth2/callback/google',
        status: '/oauth2/status',
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT ?? 8080,
        appUrl: process.env.APP_URL,
      }
    };
  }

  @Get('oauth2/authorize/google')
  @ApiOperation({ summary: 'Initiate Google OAuth2 login with dynamic redirect_uri' })
  @ApiResponse({ status: 302, description: 'Redirect to Google OAuth' })
  async oauth2AuthorizeGoogle(@Query('redirect_uri') redirectUri: string, @Req() req: Request, @Res() res: Response) {
    const allowedUris: string[] = this.configService.get('googleOAuth.authorizedRedirectUris') ?? [];
    if (!redirectUri || !allowedUris.includes(redirectUri)) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Invalid or missing redirect_uri',
      });
    }
    // Store the redirect_uri in the session for use after callback
    ((req as any).session).oauth2RedirectUri = redirectUri;
    // Initiate Google OAuth (redirect to /auth/google, which triggers the AuthGuard)
    return res.redirect('/api/v1/auth/google');
  }

  @Get('oauth2/callback/google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({ summary: 'Google OAuth2 callback' })
  @ApiResponse({ status: 200, description: 'Google authentication successful' })
  @ApiResponse({ status: 400, description: 'Authentication failed' })
  async googleAuthCallback(@Req() req: Request, @Res() res: Response) {
    try {
      const googleUser = req.user as any;
      if (!googleUser) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'No user data received from Google OAuth',
        });
      }
      const ipAddress = req.ip ?? req.socket?.remoteAddress ?? 'unknown';
      const deviceDetails = req.headers['user-agent'] ?? 'unknown';
      const result = await this.authService.googleAuth(googleUser, ipAddress, deviceDetails);
      // Use the redirect_uri from the session if present and valid
      const allowedUris: string[] = this.configService.get('googleOAuth.authorizedRedirectUris') ?? [];
      let frontendRedirectUri = ((req as any).session).oauth2RedirectUri;
      if (!frontendRedirectUri || !allowedUris.includes(frontendRedirectUri)) {
        // fallback to first allowed frontend URI
        frontendRedirectUri = allowedUris.find((uri: string) => uri.startsWith('http') && !uri.includes('/oauth2/callback/google'));
      }
      if (req.headers.accept?.includes('text/html') && frontendRedirectUri) {
        const queryParams = new URLSearchParams({
          sessionToken: result.sessionToken,
          refreshToken: result.refreshToken,
          userId: result.user.id,
          expiresAt: result.expiresAt,
          refreshExpiresAt: result.refreshExpiresAt,
        });
        // Clear the session value after use
        ((req as any).session).oauth2RedirectUri = undefined;
        return res.redirect(`${frontendRedirectUri}?${queryParams.toString()}`);
      }
      return res.status(HttpStatus.OK).json({
        success: true,
        message: 'Google authentication successful',
        ...result,
      });
    } catch (error) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Google authentication failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  @Get('oauth2/debug')
  @ApiOperation({ summary: 'Debug OAuth2 configuration and test connection' })
  @ApiResponse({ status: 200, description: 'Debug information' })
  async debugOAuth2() {
    const googleConfig = this.configService.get('googleOAuth');
    
    return {
      success: true,
      message: 'OAuth2 Debug Information',
      timestamp: new Date().toISOString(),
      configuration: {
        clientId: googleConfig.clientId ? 'Configured' : 'Missing',
        clientSecret: googleConfig.clientSecret ? 'Configured' : 'Missing',
        redirectUri: googleConfig.redirectUri,
        scope: googleConfig.scope,
        authorizedRedirectUris: googleConfig.authorizedRedirectUris,
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT ?? 8080,
        appUrl: process.env.APP_URL,
      },
      endpoints: {
        authorize: '/auth/google',
        callback: '/oauth2/callback/google',
        status: '/oauth2/status',
        debug: '/oauth2/debug',
      },
      troubleshooting: {
        checkClientId: 'Ensure GOOGLE_CLIENT_ID is set in .env',
        checkClientSecret: 'Ensure GOOGLE_CLIENT_SECRET is set in .env',
        checkRedirectUri: 'Ensure redirect URI matches Google Cloud Console',
        checkAuthorizedUris: 'Ensure authorized URIs include your callback URL',
      }
    };
  }

  @Get('email/status')
  @ApiOperation({ summary: 'Get email provider status and usage limits' })
  @ApiResponse({ status: 200, description: 'Email provider status retrieved' })
  async getEmailStatus() {
    const emailService = this.authService.getEmailService();
    const emailStatus = emailService.getEmailProviderStatus();
    
    return {
      success: true,
      message: 'Email provider status',
      timestamp: new Date().toISOString(),
      emailProviders: emailStatus,
      recommendations: {
        gmailLimit: 'Gmail has a 500 email/day limit for regular accounts, 2000 for Google Workspace.',
        googleWorkspace: 'Upgrade to Google Workspace for higher daily limits (2000 emails/day).',
        emailQueuing: 'Consider implementing email queuing to retry failed emails later.',
        multipleAccounts: 'Use multiple Gmail accounts and rotate them when limits are reached.',
        monitoring: 'Monitor usage regularly to avoid hitting daily limits.',
      }
    };
  }
}
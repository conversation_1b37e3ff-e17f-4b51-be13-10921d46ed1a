apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-nest-backend
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/part-of: ai-nest-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/ChidhagniConsulting/ai-nest-backend
    targetRevision: HEAD
    path: k8s
  destination:
    server: https://kubernetes.default.svc
    namespace: ai-nest-backend
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "AI Nest Backend - NestJS Authentication API with PostgreSQL"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/ai-nest-backend"
  - name: Environment
    value: "Production"
